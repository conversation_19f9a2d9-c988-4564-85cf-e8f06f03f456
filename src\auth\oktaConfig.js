// Okta configuration
export const oktaConfig = {
  // Auth0 issuer URL - make sure it has a trailing slash
  issuer: 'https://dev-h4miq62iedog2a6m.us.auth0.com/',
  clientId: 'BDEQBKCbIxFjpcDfqK5PWeI461jKCQ5m',
  redirectUri: typeof window !== 'undefined' ? window.location.origin + '/login/callback' : '',
  scopes: ['openid', 'profile', 'email'],
  responseType: ['token', 'id_token'],
  pkce: true,
  tokenManager: {
    storage: 'localStorage',
    autoRenew: true
  },
  cookies: {
    secure: false,
    sameSite: 'lax'
  },
  devMode: process.env.NODE_ENV !== 'production',
  disableHttpsCheck: process.env.NODE_ENV !== 'production'
};
