// custom typefaces
import "typeface-montserrat"
import "typeface-merriweather"
import React from 'react';
import { Security } from '@okta/okta-react';
import { OktaAuth } from '@okta/okta-auth-js';
import { oktaConfig } from './src/auth/oktaConfig';
import { navigate } from 'gatsby';
import { GlobalProvider } from './src/context/GlobalContext';
import { GlobalStyle } from './src/styles/GlobalStyle';

// Initialize the Okta Auth client
let oktaAuth = null;

// Initialize Okta Auth in a browser-safe way
if (typeof window !== 'undefined') {
  try {
    console.log('Initializing Okta Auth with config:', oktaConfig);
    oktaAuth = new OktaAuth(oktaConfig);

    // Store oktaAuth globally for easy access
    window._oktaAuth = oktaAuth;

    // Log the initialized instance
    console.log('Okta Auth initialized successfully:', {
      issuer: oktaAuth.options.issuer,
      clientId: oktaAuth.options.clientId,
      redirectUri: oktaAuth.options.redirectUri,
      scopes: oktaAuth.options.scopes
    });
  } catch (error) {
    console.error('Error initializing Okta Auth:', error);
  }
}

export const wrapRootElement = ({ element }) => {
  // Function to handle when authentication is required
  const onAuthRequired = () => {
    navigate('/login');
  };

  // Function to restore the original URI after login
  const restoreOriginalUri = async (_oktaAuth, originalUri) => {
    navigate(originalUri || '/');
  };

  // If window is not defined (during SSR), return the element without Okta
  if (typeof window === 'undefined' || !oktaAuth) {
    return (
      <GlobalProvider>
        <GlobalStyle />
        {element}
      </GlobalProvider>
    );
  }

  return (
    <Security
      oktaAuth={oktaAuth}
      onAuthRequired={onAuthRequired}
      restoreOriginalUri={restoreOriginalUri}
    >
      <GlobalProvider>
        <GlobalStyle />
        {element}
      </GlobalProvider>
    </Security>
  );
};
