// custom typefaces
import "typeface-montserrat"
import "typeface-merriweather"
import React, { Suspense, lazy } from 'react';
import { GlobalProvider } from './src/context/GlobalContext';
import { GlobalStyle } from './src/styles/GlobalStyle';
import ClientOnly from './src/components/auth-provider/client-only';

const Auth0ProviderWithHistory = lazy(() => import('./src/components/auth-provider/auth-provider'));

export const wrapRootElement = ({ element }) => (
  <ClientOnly>
    <Suspense fallback={<div>Loading auth...</div>}>
    <GlobalProvider>
      <GlobalStyle />
      <Auth0ProviderWithHistory>
        {element}
      </Auth0ProviderWithHistory>
    </GlobalProvider>
    </Suspense>
  </ClientOnly>
);
