// custom typefaces
import "typeface-montserrat"
import "typeface-merriweather"
import React from 'react';
import { Security } from '@okta/okta-react';
import { OktaAuth } from '@okta/okta-auth-js';
import { oktaConfig } from './src/auth/oktaConfig';
import { navigate } from 'gatsby';
import { GlobalProvider } from './src/context/GlobalContext';
import { GlobalStyle } from './src/styles/GlobalStyle';

// Initialize the Okta Auth client
const oktaAuth = typeof window !== 'undefined'
  ? new OktaAuth(oktaConfig)
  : null;

// Store oktaAuth globally for easy access
if (typeof window !== 'undefined') {
  window._oktaAuth = oktaAuth;
}

export const wrapRootElement = ({ element }) => {
  // Function to handle when authentication is required
  const onAuthRequired = () => {
    navigate('/login');
  };

  // Function to restore the original URI after login
  const restoreOriginalUri = async (_oktaAuth, originalUri) => {
    navigate(originalUri || '/');
  };

  // If window is not defined (during SSR), return the element without Ok<PERSON>
  if (typeof window === 'undefined' || !oktaAuth) {
    return (
      <GlobalProvider>
        <GlobalStyle />
        {element}
      </GlobalProvider>
    );
  }

  return (
    <Security
      oktaAuth={oktaAuth}
      onAuthRequired={onAuthRequired}
      restoreOriginalUri={restoreOriginalUri}
    >
      <GlobalProvider>
        <GlobalStyle />
        {element}
      </GlobalProvider>
    </Security>
  );
};
