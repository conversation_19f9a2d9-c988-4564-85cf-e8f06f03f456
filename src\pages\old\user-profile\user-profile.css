.user-profile {
  display: flex;
  align-items: center;
  position: relative;
}

.user-profile__logged-in {
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
}

.user-profile__avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-bold);
}

.user-profile__initials {
  font-size: 14px;
}

.user-profile__dropdown {
  position: absolute;
  top: 40px;
  right: 0;
  background-color: var(--color-background-primary);
  border-radius: var(--border-radius-small);
  box-shadow: var(--shadow-medium);
  padding: 8px 0;
  min-width: 120px;
  z-index: 100;
  display: none;
}

.user-profile__logged-in:hover .user-profile__dropdown {
  display: block;
}

.user-profile__auth-links {
  display: flex;
  align-items: center;
}

.user-profile__link {
  color: var(--color-text-light);
  text-decoration: none;
  font-size: var(--font-size-small);
  padding: 8px 12px;
  transition: color var(--duration-fast) var(--animation-timing-default);
}

.user-profile__link:hover {
  color: var(--color-text-primary);
  text-decoration: none;
}

.user-profile__separator {
  color: var(--color-text-light);
  margin: 0 4px;
}