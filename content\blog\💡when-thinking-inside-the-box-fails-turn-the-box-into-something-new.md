---
path: innovation_in_box
date: 2025-05-06T11:40:10.697Z
title: 💡When Thinking Inside the Box Fails, Turn the Box Into Something New
description: A playful yet insightful look at how innovation thrives when we
  stop overcomplicating and start rethinking—from the battlefield to the
  boardroom. Featuring lessons from Team of Teams and Mc<PERSON>insey, this piece
  explores why simplicity, creativity, and empowered teams drive real progress.
status: public
---
In today’s world of constant disruption, “thinking outside the box” has become more than a cliché—it's become a survival skill. But what if the real problem isn’t just the box… it's the belief that we're supposed to stay near it?

Some problems are just waiting to be overcomplicated. We throw tools at them. We schedule meetings about them. We create slides *about* the meetings about them. And then someone—usually the intern—solves it with a paperclip and common sense.

<iframe src="https://www.facebook.com/plugins/video.php?height=476&href=https%3A%2F%2Fwww.facebook.com%2Funify%2Fvideos%2F2148792732199412%2F&show_text=false&width=332&t=0" width="332" height="476" style="border:none;overflow:hidden" scrolling="no" frameborder="0" allowfullscreen="true" allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share" allowFullScreen="true"></iframe>

That’s the vibe of this  video gem.[](https://www.facebook.com/unify/videos/2148792732199412/) What looks like a failure of design becomes a masterclass in simplicity. No AI. No multi-cloud orchestration. Just one clever pivot that makes you say, “Oh. Well that was obvious… eventually.”

The solution feels *obvious* only after you've seen it—like all great ideas do.

It reminded me of a powerful insight from *Team of Teams* by Gen. Stanley McChrystal:

> *“In complex environments, efficiency is no longer enough. We must prioritize adaptability over perfection.”*

In tightly controlled systems, where roles are clearly defined and procedures are optimized to the decimal, innovation often becomes the first casualty. We may feel productive—but only in the way a hamster does in a wheel.

Translation? You can be highly efficient at running the wrong direction or not moving at all. 

### McKinsey Says You're Probably Doing Too Much

McKinsey’s research says:

> Organizations that foster innovation through agility and decentralized decision-making outperform competitors by **over 2x in revenue growth.**
> Yet, only 10% of companies report they’re achieving their full innovation potential.

According to McKinsey, organizations that embrace agile, decentralized, human-first innovation outperform their competitors by over 2x in revenue growth. That’s the good news.

The bad news? Most companies are still trying to solve strategic dysfunction with more Jira boards and a new Slack emoji.

McKinsey doesn’t say that *explicitly*, but it’s definitely between the lines.

So what’s holding the rest back?

### Stop Over-Engineering. Start Over-Imagining.

In the video, the breakthrough doesn’t come from more gears, tighter bolts, or a Kubernetes cluster. It comes from *rethinking the motion entirely*. Lateral thinking. The kind that gets eye-rolls in a sprint retro for “not aligning with acceptance criteria.”

We tend to treat innovation like a separate function—a lab, a team, a project—rather than a mindset baked into every layer of the organization. We over-architect solutions, box ourselves into rigid frameworks, and default to "how it’s always been done." All the while, real innovation might be one absurd idea away.

Complex systems (organizations, teams, markets) don’t need more rigid control. They need **shared consciousness and empowered execution**—a decentralized model where people are trusted to act in the moment based on the best available context.

> As McChrystal puts it: *“The role of the leader is no longer to control everything directly, but to shape the ecosystem where others can flourish.”*

Basically, make it easier for people to do the right thing—and harder to build 87-slide decks on why they didn’t.

### The Power of Lateral Thinking

In the video, a mechanical “failure” transforms into a design win—without new tech, just a new perspective. Just a better question.

This is the power of lateral thinking: where simplicity, clarity, and the courage to challenge assumptions beat complexity every time.

It’s a reminder that:

* Innovation isn’t always about more tools—it’s about better questions. It’s an attitude. 
* Culture eats strategy—and bureaucracy—for breakfast.
* Simplicity is not the enemy of sophistication. It’s the gateway to it. And delicious (like pizza 🍕 ).

- - -

So next time you’re stuck in a maze of overengineered solutions, zoom out. Maybe the problem isn’t unsolvable. Maybe it’s just misunderstood.

Next time you're spinning up another dashboard to track 14 KPIs on “innovation,” ask yourself:
🤔 *Is this helping… or is it just a fancier box?*

Sometimes, the smartest move is to set the box on fire (metaphorically), roast some marshmallows, and sketch a new idea on a napkin.

✨ *Innovation doesn’t just ask us to think outside the box—it dares us to question why the box exists at all.*

*Rethink the problem. Reimagine the box. Let simplicity lead.*