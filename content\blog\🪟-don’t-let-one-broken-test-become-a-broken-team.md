---
path: brocken_window
date: 2020-11-13T05:00:00.000Z
title: 🪟 Don’t Let One Broken Test Become a Broken Team
description: Small things aren’t small when left unchecked. In code, like in
  buildings, neglect invites decay.This isn’t about perfection—it’s about
  intention. A messy codebase becomes a cultural message. What messages are your
  broken windows sending?
---
**🪟 Broken Window Theory: Addressing Small Issues Promptly**

*The Pragmatic Programmer* discusses the importance of maintaining quality and addressing minor issues before they escalate:

> "One broken window, left unrepaired for any substantial length of time, instills in the inhabitants of the building a sense of abandonment—a sense that the powers that be don’t care about the building. So another window gets broken. People start littering. Graffiti appears. Serious structural damage begins. In a relatively short span of time, the building becomes damaged beyond the owner’s desire to fix it, and the sense of abandonment becomes reality."

📖 *Excerpt from* **[“The Pragmatic Programmer” by <PERSON> and <PERSON>](https://pragprog.com/titles/tpp20/the-pragmatic-programmer-20th-anniversary-edition/)**

- - -

**🧠 Takeaway:**\
In software development and beyond, neglecting small problems can lead to a culture of apathy and decline. Proactively fixing issues, no matter how minor, fosters a sense of care and responsibility.

From that point on, technical debt compounds. Quick fixes replace clean solutions. Standards slide. Pride in craftsmanship fades. As *The Pragmatic Programmer* wisely puts it:

> *“Don’t live with broken windows. Fix bad designs, wrong decisions, and poor code when you see them.”*

This isn't about chasing perfection—it's about creating a culture of care.  
Fixing a small bug, refactoring a confusing function, or updating that documentation shows others that quality matters. And that sense of ownership? It’s contagious.

---

**👨‍💻 In practice:**  
- Fix that broken test instead of commenting it out.  
- Refactor when adding features—don’t just pile on.  
- Keep tools, dependencies, and docs clean and up to date.  
- Treat TODOs like time bombs, not footnotes.  
- Celebrate those who tidy up the messes, not just ship new features.

Clean code encourages clean thinking. And clean thinking leads to resilient systems—and happier developers.
