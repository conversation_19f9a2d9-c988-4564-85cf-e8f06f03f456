import React, { useEffect } from 'react';
import { navigate } from 'gatsby';
import Layout from '../components/layout';
import SEO from '../components/seo';
import { useOktaAuth } from '@okta/okta-react';

const Logout = ({ location }) => {
  // Handle SSR case
  const oktaAuthResult = typeof window !== 'undefined' ? useOktaAuth() : { oktaAuth: null };
  const { oktaAuth } = oktaAuthResult || {};

  // Get the return URL from the query string or default to homepage
  const returnTo =
    (typeof window !== 'undefined' ? new URLSearchParams(location.search).get('returnTo') : null) ||
    '/';

  useEffect(() => {
    // Only run in browser environment
    if (typeof window !== 'undefined') {
      // Logout the user if oktaAuth is available
      if (oktaAuth) {
        oktaAuth.signOut();
      }

      // Redirect to the return URL after a short delay
      const timer = setTimeout(() => {
        navigate(returnTo);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [oktaAuth, returnTo]);

  return (
    <Layout title="Logging Out">
      <SEO title="Logging Out" />
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
        textAlign: 'center',
        padding: '2rem'
      }}>
        <h1>Logging Out</h1>
        <p>Please wait while we log you out...</p>
        <div className="loading-spinner" style={{
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          width: '30px',
          height: '30px',
          animation: 'spin 2s linear infinite',
          marginTop: '1rem'
        }}></div>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    </Layout>
  );
};

export default Logout;

