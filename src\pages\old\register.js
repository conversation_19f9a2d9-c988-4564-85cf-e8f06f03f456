import React, { useState, useEffect } from "react"
import { Link, navigate } from "gatsby"
import Layout from "../../components/layout"
import SEO from "../../components/seo"
import styled from "styled-components"
// import { FaUserPlus } from "react-icons/fa"

const RegisterPage = ({ location }) => {
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [error, setError] = useState(null)
  const [loading, setLoading] = useState(false)

  // Get return URL from query parameters
  const params = new URLSearchParams(location.search)
  const returnTo = params.get('returnTo') || "/"

  // Check if user is already logged in
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const loggedInUser = localStorage.getItem("username")
      if (loggedInUser) {
        navigate(returnTo)
      }
    }
  }, [returnTo])

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    if (password !== confirmPassword) {
      setError("Passwords do not match")
      setLoading(false)
      return
    }

    try {
      // Accept any credentials without validation

      // Store username in localStorage
      localStorage.setItem("username", username)

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800))

      // Redirect to return URL
      navigate(returnTo)
    } catch (err) {
      setError("Failed to create account. Please try again.")
      setLoading(false)
    }
  }

  return (
    <Layout location={location} title="Sign Up">
      <SEO title="Sign Up" />
      <RegisterContainer>
        <h1>Create Account</h1>
        {error && <ErrorMessage>{error}</ErrorMessage>}
        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
            />
          </FormGroup>
          <FormGroup>
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </FormGroup>
          <FormGroup>
            <Label htmlFor="confirmPassword">Confirm Password</Label>
            <Input
              id="confirmPassword"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
            />
          </FormGroup>
          <Button type="submit" disabled={loading}>
            {loading ? "Creating account..." : "Sign Up"}
          </Button>
        </Form>
        <LoginLink>
          Already have an account? <Link to={`/login?returnTo=${encodeURIComponent(returnTo)}`}>Sign In</Link>
        </LoginLink>
      </RegisterContainer>
    </Layout>
  )
}

const RegisterContainer = styled.div`
  max-width: 400px;
  margin: 0 auto;
  padding: 2rem 1rem;
`

const Form = styled.form`
  margin-bottom: 1.5rem;
`

const FormGroup = styled.div`
  margin-bottom: 1rem;
`

const Label = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
`

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-small);
  font-size: 1rem;
`

const Button = styled.button`
  width: 100%;
  padding: 0.75rem;
  background-color: var(--color-secondary);
  color: white;
  border: none;
  border-radius: var(--border-radius-small);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: var(--color-secondary-dark);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
`

const ErrorMessage = styled.div`
  color: var(--color-error);
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: var(--color-error-bg);
  border-radius: var(--border-radius-small);
`

const LoginLink = styled.div`
  text-align: center;
  font-size: 0.9rem;
`

export default RegisterPage


