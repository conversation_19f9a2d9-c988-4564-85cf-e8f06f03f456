import React, { useEffect } from 'react';
import { useOktaAuth } from '@okta/okta-react';
import Layout from '../components/layout';
import SEO from '../components/seo';

// Client-side only component that handles the actual login redirect
const LoginHandler = () => {
  const { oktaAuth, authState } = useOktaAuth();

  useEffect(() => {
    // If already authenticated, redirect to home
    if (authState?.isAuthenticated) {
      window.location.href = '/';
      return;
    }

    // Redirect to Okta login
    if (oktaAuth) {
      console.log('Initiating login redirect with Okta...');

      // Clear any existing Okta transaction data to prevent stale data issues
      if (window.localStorage) {
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('okta-oauth-')) {
            localStorage.removeItem(key);
          }
        });
      }

      // Attempt the redirect with a small delay to ensure everything is initialized
      const redirectTimer = setTimeout(() => {
        try {
          // Redirect to Okta login page
          oktaAuth.signInWithRedirect({ originalUri: '/' })
            .catch(error => {
              console.error('Error during login redirect:', error);
              document.getElementById('login-error').style.display = 'block';
              document.getElementById('login-error-message').textContent =
                `Login error: ${error.message || 'Unknown error'}`;
            });
        } catch (error) {
          console.error('Exception during login redirect:', error);
          document.getElementById('login-error').style.display = 'block';
          document.getElementById('login-error-message').textContent =
            `Login error: ${error.message || 'Unknown error'}`;
        }
      }, 300);

      return () => clearTimeout(redirectTimer);
    }
  }, [oktaAuth, authState]);

  return (
    <div id="login-error" style={{ display: 'none', color: 'red', marginTop: '2rem', textAlign: 'center' }}>
      <p>There was a problem redirecting to the login page.</p>
      <p id="login-error-message"></p>
      <p>Please check the browser console for more details or try again.</p>
    </div>
  );
};

const Login = () => {
  const isBrowser = typeof window !== 'undefined';
  const { oktaAuth, authState } = useOktaAuth();

  // Function to handle login button click
  const handleLogin = async () => {
    try {
      if (oktaAuth) {
        console.log('Login button clicked, oktaAuth instance:', oktaAuth);

        // Clear any existing Okta transaction data
        if (window.localStorage) {
          Object.keys(localStorage).forEach(key => {
            if (key.startsWith('okta-oauth-') || key.startsWith('okta-token-storage')) {
              localStorage.removeItem(key);
            }
          });
        }

        // Log the configuration for debugging
        console.log('Okta configuration:', {
          issuer: oktaAuth.options.issuer,
          clientId: oktaAuth.options.clientId,
          redirectUri: oktaAuth.options.redirectUri,
          scopes: oktaAuth.options.scopes
        });

        // Create a direct login URL for testing
        const directLoginUrl = `${oktaAuth.options.issuer}authorize?` +
          `client_id=${oktaAuth.options.clientId}&` +
          `response_type=code&` +
          `scope=${encodeURIComponent(oktaAuth.options.scopes.join(' '))}&` +
          `redirect_uri=${encodeURIComponent(oktaAuth.options.redirectUri)}&` +
          `state=login${Date.now()}`;

        console.log('Direct login URL for testing:', directLoginUrl);

        // Redirect to Okta login page
        await oktaAuth.signInWithRedirect({ originalUri: '/' });
      } else {
        console.error('No oktaAuth instance available');
        document.getElementById('login-error').style.display = 'block';
        document.getElementById('login-error-message').textContent = 'Authentication service not initialized';
      }
    } catch (error) {
      console.error('Error during login redirect:', error);
      document.getElementById('login-error').style.display = 'block';
      document.getElementById('login-error-message').textContent =
        `Login error: ${error.message || 'Unknown error'}`;
    }
  };

  // If already authenticated, show a message
  if (authState?.isAuthenticated) {
    return (
      <Layout title="Login">
        <SEO title="Login" />
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '50vh',
          textAlign: 'center',
          padding: '2rem'
        }}>
          <h1>You are already logged in</h1>
          <p>You can return to the <a href="/" style={{ color: '#0277bd' }}>home page</a>.</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Login">
      <SEO title="Login" />
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
        textAlign: 'center',
        padding: '2rem'
      }}>
        <h1>Login to My Blog</h1>
        <p>Please log in to access all features of the blog.</p>

        {/* Login button */}
        <button
          onClick={handleLogin}
          style={{
            background: '#0277bd',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '1rem',
            marginTop: '1.5rem'
          }}
        >
          Login with Auth0
        </button>

        {/* Only render the LoginHandler component on the client side for automatic redirect */}
        {isBrowser && <LoginHandler />}

        {/* Error display */}
        <div id="login-error" style={{ display: 'none', color: 'red', marginTop: '2rem', textAlign: 'center' }}>
          <p>There was a problem redirecting to the login page.</p>
          <p id="login-error-message"></p>
          <p>Please check the browser console for more details or try again.</p>

          <div style={{ marginTop: '1.5rem' }}>
            <p>Alternatively, you can try the manual login:</p>
            <a
              href="/manual-login"
              style={{
                display: 'inline-block',
                background: '#2e7d32',
                color: 'white',
                textDecoration: 'none',
                padding: '0.5rem 1rem',
                borderRadius: '4px',
                marginTop: '0.5rem'
              }}
            >
              Manual Auth0 Login
            </a>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Login;
