import React, { useEffect } from 'react';
import { useOktaAuth } from '@okta/okta-react';
import Layout from '../components/layout';
import SEO from '../components/seo';

// Client-side only component that handles the actual login redirect
const LoginHandler = () => {
  const { oktaAuth, authState } = useOktaAuth();

  useEffect(() => {
    // If already authenticated, redirect to home
    if (authState?.isAuthenticated) {
      window.location.href = '/';
      return;
    }

    // Redirect to Okta login
    if (oktaAuth) {
      console.log('Initiating login redirect with Okta...');

      // Save the current URL to return to after login - use localStorage for better persistence
      localStorage.setItem('okta-return-url', '/');

      // Clear any existing Okta transaction data to prevent stale data issues
      if (window.localStorage) {
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('okta-oauth-')) {
            localStorage.removeItem(key);
          }
        });
      }

      // Add a small delay to ensure everything is initialized
      const redirectTimer = setTimeout(() => {
        try {
          console.log('Okta configuration:', {
            redirectUri: oktaAuth.options.redirectUri,
            issuer: oktaAuth.options.issuer,
            clientId: oktaAuth.options.clientId
          });

          // Log the Okta configuration for debugging
          console.log('Okta configuration before redirect:', {
            redirectUri: oktaAuth.options.redirectUri,
            issuer: oktaAuth.options.issuer,
            clientId: oktaAuth.options.clientId,
            disableHttpsCheck: oktaAuth.options.disableHttpsCheck
          });

          // Try the simpler version of signInWithRedirect
          try {
            // Log detailed information about the Okta instance
            console.log('Okta instance details:', {
              redirectUri: oktaAuth.options.redirectUri,
              issuer: oktaAuth.options.issuer,
              clientId: oktaAuth.options.clientId,
              storageManager: oktaAuth.options.storageManager,
              tokenManager: oktaAuth.options.tokenManager
            });

            // Create a direct login URL for testing
            const directLoginUrl = `${oktaAuth.options.issuer}authorize?` +
              `client_id=${oktaAuth.options.clientId}&` +
              `response_type=code&` +
              `scope=${encodeURIComponent(oktaAuth.options.scopes.join(' '))}&` +
              `redirect_uri=${encodeURIComponent(oktaAuth.options.redirectUri)}&` +
              `state=directlogin${Date.now()}`;

            console.log('Direct login URL for testing:', directLoginUrl);

            // Store the direct login URL for the debug panel
            window._directLoginUrl = directLoginUrl;

            // Attempt the redirect
            oktaAuth.signInWithRedirect()
              .catch(error => {
                console.error('Error during login redirect:', error);
                // Log the full error object for debugging
                console.error('Full error object:', JSON.stringify(error, null, 2));
                document.getElementById('login-error').style.display = 'block';
                document.getElementById('login-error-message').textContent =
                  `Login error: ${error.message || 'Unknown error'}`;

                // Update the debug info to show the direct login link
                if (document.getElementById('direct-login-link')) {
                  document.getElementById('direct-login-link').style.display = 'block';
                }
              });
          } catch (error) {
            console.error('Exception during login redirect setup:', error);
            // Log the full error object for debugging
            console.error('Full error object:', JSON.stringify(error, Object.getOwnPropertyNames(error).reduce((acc, key) => {
              acc[key] = error[key];
              return acc;
            }, {}), 2));
            document.getElementById('login-error').style.display = 'block';
            document.getElementById('login-error-message').textContent =
              `Login error: ${error.message || 'Unknown error'} (${error.name || 'No error name'})`;

            // Update the debug info to show the direct login link
            if (document.getElementById('direct-login-link')) {
              document.getElementById('direct-login-link').style.display = 'block';
            }
          }
        } catch (error) {
          console.error('Exception during login redirect:', error);
          document.getElementById('login-error').style.display = 'block';
          document.getElementById('login-error-message').textContent =
            `Login error: ${error.message || 'Unknown error'}`;
        }
      }, 300);

      return () => clearTimeout(redirectTimer);
    }
  }, [oktaAuth, authState]);

  return (
    <div id="login-error" style={{ display: 'none', color: 'red', marginTop: '2rem', textAlign: 'center' }}>
      <p>There was a problem redirecting to the login page.</p>
      <p id="login-error-message"></p>
      <p>Please check the browser console for more details or try again.</p>
    </div>
  );
};

const Login = () => {
  const isBrowser = typeof window !== 'undefined';
  const [showDebugInfo, setShowDebugInfo] = React.useState(false);

  return (
    <Layout title="Login">
      <SEO title="Login" />
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
        textAlign: 'center',
        padding: '2rem'
      }}>
        <h1>Redirecting to Login...</h1>
        <p>Please wait while we redirect you to the login page.</p>
        {/* Only render the LoginHandler component on the client side */}
        {isBrowser && <LoginHandler />}
        <div className="loading-spinner" style={{
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          width: '30px',
          height: '30px',
          animation: 'spin 2s linear infinite',
          marginTop: '1rem'
        }}></div>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>

        {/* Debug button */}
        <div style={{ marginTop: '2rem' }}>
          <button
            onClick={() => setShowDebugInfo(!showDebugInfo)}
            style={{
              background: '#f0f0f0',
              border: '1px solid #ccc',
              padding: '0.5rem 1rem',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            {showDebugInfo ? 'Hide Debug Info' : 'Show Debug Info'}
          </button>

          {showDebugInfo && (
            <div style={{
              marginTop: '1rem',
              textAlign: 'left',
              background: '#f8f8f8',
              padding: '1rem',
              borderRadius: '4px',
              maxWidth: '600px'
            }}>
              <h3>Debug Information</h3>
              <p><strong>Current URL:</strong> {isBrowser ? window.location.href : 'N/A'}</p>
              <p><strong>Redirect URI:</strong> {isBrowser ? `${window.location.origin}/login/callback` : 'N/A'}</p>
              <p><strong>Environment:</strong> {process.env.NODE_ENV}</p>
              <p>
                <strong>Troubleshooting:</strong> If login is not working, please check:
              </p>
              <ul>
                <li>Okta configuration in your developer dashboard</li>
                <li>Browser console for any errors</li>
                <li>Network requests to see if the redirect is happening</li>
                <li>CORS settings in your Okta application</li>
              </ul>
              <p>
                <strong>Manual Login:</strong> If automatic redirect is not working, you can try to
                <a href="https://dev-h4miq62iedog2a6m.us.auth0.com/authorize?client_id=BDEQBKCbIxFjpcDfqK5PWeI461jKCQ5m&response_type=code&scope=openid+profile+email&redirect_uri=http://localhost:8000/login/callback&state=somestate123"
                   style={{ marginLeft: '5px', color: 'blue' }}>
                  login manually
                </a>
              </p>
              <p>
                <strong>HTTP Development Login:</strong> For local development with HTTP:
                <a href="http://dev-h4miq62iedog2a6m.us.auth0.com/authorize?client_id=BDEQBKCbIxFjpcDfqK5PWeI461jKCQ5m&response_type=code&scope=openid+profile+email&redirect_uri=http://localhost:8000/login/callback&state=somestate123"
                   style={{ marginLeft: '5px', color: 'blue' }}>
                  login with HTTP
                </a>
              </p>

              <div id="direct-login-link" style={{ display: 'none', marginTop: '1rem', padding: '1rem', backgroundColor: '#fffbea', border: '1px solid #ffd700', borderRadius: '4px' }}>
                <p>
                  <strong>Direct Login Link:</strong> The automatic login failed. Try this direct login link:
                </p>
                <p>
                  <a
                    href={isBrowser && window._directLoginUrl ? window._directLoginUrl : '#'}
                    style={{ color: 'blue', wordBreak: 'break-all' }}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {isBrowser && window._directLoginUrl ? window._directLoginUrl : 'Direct login link will appear here after error'}
                  </a>
                </p>
              </div>

              <p>
                <strong>Note:</strong> For Okta to work with HTTP localhost, you need to add these to your Okta settings:
              </p>
              <ul>
                <li>Allowed Callback URL: <code>http://localhost:8000/login/callback</code></li>
                <li>Allowed Web Origin: <code>http://localhost:8000</code></li>
                <li>Add a Trusted Origin for <code>http://localhost:8000</code> with CORS and Redirect enabled</li>
              </ul>

              <p>
                <strong>Troubleshooting Auth0:</strong> If you're using Auth0 (which appears to be the case), make sure:
              </p>
              <ul>
                <li>Your Auth0 application is configured as a "Single Page Application"</li>
                <li>The "Token Endpoint Authentication Method" is set to "None"</li>
                <li>CORS is enabled for your application domain</li>
                <li>The application has the correct permissions/scopes</li>
              </ul>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Login;
