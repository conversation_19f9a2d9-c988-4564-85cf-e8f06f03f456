import React, { useEffect } from 'react';
import { useOktaAuth } from '@okta/okta-react';
import Layout from '../components/layout';
import SEO from '../components/seo';

// Client-side only component that handles the actual login redirect
const LoginHandler = () => {
  const { oktaAuth, authState } = useOktaAuth();

  useEffect(() => {
    // If already authenticated, redirect to home
    if (authState?.isAuthenticated) {
      window.location.href = '/';
      return;
    }

    // Redirect to Okta login
    if (oktaAuth) {
      console.log('Initiating login redirect with Okta...');

      // Save the current URL to return to after login - use localStorage for better persistence
      localStorage.setItem('okta-return-url', '/');

      // Clear any existing Okta transaction data to prevent stale data issues
      if (window.localStorage) {
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('okta-oauth-')) {
            localStorage.removeItem(key);
          }
        });
      }

      // Add a small delay to ensure everything is initialized
      const redirectTimer = setTimeout(() => {
        try {
          console.log('Okta configuration:', {
            redirectUri: oktaAuth.options.redirectUri,
            issuer: oktaAuth.options.issuer,
            clientId: oktaAuth.options.clientId
          });

          // Log the Okta configuration for debugging
          console.log('Okta configuration before redirect:', {
            redirectUri: oktaAuth.options.redirectUri,
            issuer: oktaAuth.options.issuer,
            clientId: oktaAuth.options.clientId,
            disableHttpsCheck: oktaAuth.options.disableHttpsCheck
          });

          // Try the simpler version of signInWithRedirect
          oktaAuth.signInWithRedirect()
            .catch(error => {
              console.error('Error during login redirect:', error);
              document.getElementById('login-error').style.display = 'block';
              document.getElementById('login-error-message').textContent =
                `Login error: ${error.message || 'Unknown error'}`;
            });
        } catch (error) {
          console.error('Exception during login redirect:', error);
          document.getElementById('login-error').style.display = 'block';
          document.getElementById('login-error-message').textContent =
            `Login error: ${error.message || 'Unknown error'}`;
        }
      }, 300);

      return () => clearTimeout(redirectTimer);
    }
  }, [oktaAuth, authState]);

  return (
    <div id="login-error" style={{ display: 'none', color: 'red', marginTop: '2rem', textAlign: 'center' }}>
      <p>There was a problem redirecting to the login page.</p>
      <p id="login-error-message"></p>
      <p>Please check the browser console for more details or try again.</p>
    </div>
  );
};

const Login = () => {
  const isBrowser = typeof window !== 'undefined';
  const [showDebugInfo, setShowDebugInfo] = React.useState(false);

  return (
    <Layout title="Login">
      <SEO title="Login" />
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
        textAlign: 'center',
        padding: '2rem'
      }}>
        <h1>Redirecting to Login...</h1>
        <p>Please wait while we redirect you to the login page.</p>
        {/* Only render the LoginHandler component on the client side */}
        {isBrowser && <LoginHandler />}
        <div className="loading-spinner" style={{
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          width: '30px',
          height: '30px',
          animation: 'spin 2s linear infinite',
          marginTop: '1rem'
        }}></div>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>

        {/* Debug button */}
        <div style={{ marginTop: '2rem' }}>
          <button
            onClick={() => setShowDebugInfo(!showDebugInfo)}
            style={{
              background: '#f0f0f0',
              border: '1px solid #ccc',
              padding: '0.5rem 1rem',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            {showDebugInfo ? 'Hide Debug Info' : 'Show Debug Info'}
          </button>

          {showDebugInfo && (
            <div style={{
              marginTop: '1rem',
              textAlign: 'left',
              background: '#f8f8f8',
              padding: '1rem',
              borderRadius: '4px',
              maxWidth: '600px'
            }}>
              <h3>Debug Information</h3>
              <p><strong>Current URL:</strong> {isBrowser ? window.location.href : 'N/A'}</p>
              <p><strong>Redirect URI:</strong> {isBrowser ? `${window.location.origin}/login/callback` : 'N/A'}</p>
              <p><strong>Environment:</strong> {process.env.NODE_ENV}</p>
              <p>
                <strong>Troubleshooting:</strong> If login is not working, please check:
              </p>
              <ul>
                <li>Okta configuration in your developer dashboard</li>
                <li>Browser console for any errors</li>
                <li>Network requests to see if the redirect is happening</li>
                <li>CORS settings in your Okta application</li>
              </ul>
              <p>
                <strong>Manual Login:</strong> If automatic redirect is not working, you can try to
                <a href="https://dev-h4miq62iedog2a6m.us.auth0.com/authorize?client_id=BDEQBKCbIxFjpcDfqK5PWeI461jKCQ5m&response_type=code&scope=openid+profile+email&redirect_uri=http://localhost:8000/login/callback&state=somestate123"
                   style={{ marginLeft: '5px', color: 'blue' }}>
                  login manually
                </a>
              </p>
              <p>
                <strong>HTTP Development Login:</strong> For local development with HTTP:
                <a href="http://dev-h4miq62iedog2a6m.us.auth0.com/authorize?client_id=BDEQBKCbIxFjpcDfqK5PWeI461jKCQ5m&response_type=code&scope=openid+profile+email&redirect_uri=http://localhost:8000/login/callback&state=somestate123"
                   style={{ marginLeft: '5px', color: 'blue' }}>
                  login with HTTP
                </a>
              </p>
              <p>
                <strong>Note:</strong> For Okta to work with HTTP localhost, you need to add these to your Okta settings:
              </p>
              <ul>
                <li>Allowed Callback URL: <code>http://localhost:8000/login/callback</code></li>
                <li>Allowed Web Origin: <code>http://localhost:8000</code></li>
                <li>Add a Trusted Origin for <code>http://localhost:8000</code> with CORS and Redirect enabled</li>
              </ul>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Login;
