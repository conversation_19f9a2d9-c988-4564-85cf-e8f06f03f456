import React, { useEffect } from 'react';
import { useOktaAuth } from '@okta/okta-react';
import Layout from '../components/layout';
import SEO from '../components/seo';

// Client-side only component that handles the actual login redirect
const LoginHandler = () => {
  const { oktaAuth } = useOktaAuth();

  useEffect(() => {
    // Redirect to Okta login
    if (oktaAuth) {
      oktaAuth.signInWithRedirect();
    }
  }, [oktaAuth]);

  return null;
};

const Login = () => {
  const isBrowser = typeof window !== 'undefined';

  return (
    <Layout title="Login">
      <SEO title="Login" />
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
        textAlign: 'center',
        padding: '2rem'
      }}>
        <h1>Redirecting to Login...</h1>
        <p>Please wait while we redirect you to the login page.</p>
        {/* Only render the LoginHandler component on the client side */}
        {isBrowser && <LoginHandler />}
        <div className="loading-spinner" style={{
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          width: '30px',
          height: '30px',
          animation: 'spin 2s linear infinite',
          marginTop: '1rem'
        }}></div>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    </Layout>
  );
};

export default Login;
