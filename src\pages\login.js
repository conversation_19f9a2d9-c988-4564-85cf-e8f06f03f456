import React, { useEffect, useState } from 'react';
import { navigate } from 'gatsby';
import Layout from '../components/layout';
import SEO from '../components/seo';
import { getAuth0LoginUrl, isAuthenticated } from '../auth/auth0Service';

const Login = () => {
  const isBrowser = typeof window !== 'undefined';
  const [loginUrl, setLoginUrl] = useState('');
  
  useEffect(() => {
    // If already authenticated, redirect to home
    if (isBrowser && isAuthenticated()) {
      navigate('/');
    }
    
    // Generate Auth0 login URL
    if (isBrowser) {
      setLoginUrl(getAuth0LoginUrl());
    }
  }, [isBrowser]);
  
  // If already authenticated, show a message
  if (isBrowser && isAuthenticated()) {
    return (
      <Layout title="Login">
        <SEO title="Login" />
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '50vh',
          textAlign: 'center',
          padding: '2rem'
        }}>
          <h1>You are already logged in</h1>
          <p>You can return to the <a href="/" style={{ color: '#0277bd' }}>home page</a>.</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Login">
      <SEO title="Login" />
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
        textAlign: 'center',
        padding: '2rem'
      }}>
        <h1>Login to My Blog</h1>
        <p>Please log in to access all features of the blog.</p>
        
        {/* Login button */}
        <a
          href={loginUrl}
          style={{
            display: 'inline-block',
            background: '#0277bd',
            color: 'white',
            textDecoration: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '1rem',
            marginTop: '1.5rem'
          }}
        >
          Login with Auth0
        </a>
        
        <div style={{ marginTop: '2rem', maxWidth: '600px' }}>
          <h3>Having trouble logging in?</h3>
          <p>If you're experiencing issues with the login process, please try:</p>
          <ul style={{ textAlign: 'left' }}>
            <li>Clearing your browser cookies and cache</li>
            <li>Using a different browser</li>
            <li>Disabling any browser extensions that might interfere with authentication</li>
          </ul>
        </div>
      </div>
    </Layout>
  );
};

export default Login;
