import React from 'react';
import { navigate } from 'gatsby';
import { useOktaAuth } from '@okta/okta-react';

const PrivateRoute = ({ component: Component, location, ...rest }) => {
  const { authState } = useOktaAuth();
  
  // If the user is not authenticated, redirect to the login page
  // with the current path as the returnTo parameter
  if (!authState?.isAuthenticated) {
    const returnTo = location.pathname;
    navigate(`/login?returnTo=${encodeURIComponent(returnTo)}`);
    return null;
  }
  
  // If the user is authenticated, render the component
  return <Component location={location} {...rest} />;
};

export default PrivateRoute;