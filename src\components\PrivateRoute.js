import React from 'react';
import { navigate } from 'gatsby';
import { useOktaAuth } from '@okta/okta-react';

// Client-side authentication check component
const AuthCheck = ({ component: Component, location, ...rest }) => {
  const { authState } = useOktaAuth();

  // If the user is not authenticated, redirect to the login page
  if (!authState?.isAuthenticated) {
    const returnTo = location.pathname;
    navigate(`/login?returnTo=${encodeURIComponent(returnTo)}`);
    return null;
  }

  // If the user is authenticated, render the component
  return <Component location={location} {...rest} />;
};

// Main component that handles SSR
const PrivateRoute = ({ component: Component, location, ...rest }) => {
  const isBrowser = typeof window !== 'undefined';

  // During SSR, just render a loading state
  if (!isBrowser) {
    return <div>Loading...</div>;
  }

  // On the client, use the AuthCheck component
  return <AuthCheck component={Component} location={location} {...rest} />;
};

export default PrivateRoute;