---
title: MDX!
date: "2019-10-22"
description: "A post showing MDX in action"
---

import But<PERSON> from "src/components/button"

This is a post showing MDX in action. This starter now comes with MDX out-of-the-box!

```jsx
// you can write JSX in your Markdown!
<button>test</button>
```

<button>test</button>

## Custom components

You can also import custom React components locally. Make sure [gatsby-plugin-root-import](https://www.gatsbyjs.org/plugins/gatsby-plugin-root-import/) as relative paths will not work.

```jsx
// Import custom component
import Button from "src/components/button"
```

```jsx
<Button>Test</Button>
```

<Button>Test</Button>

## MDX

MDX lets you write JSX embedded inside markdown, perfect for technical blogs. MDX works with Gatsby through [gatsby-plugin-mdx](https://www.gatsbyjs.org/packages/gatsby-plugin-mdx/). You can learn more about it in the Gatsby docs: [Getting Started with MDX](https://www.gatsbyjs.org/docs/mdx/getting-started/).
