@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600&display=swap");

$red: #fa7373;
$blue: #7ba2ff;
$orange: #f1ba64;
$teal: #42a7a1;
$white: #fff;
$gray: #949494;
$step: 40px;
$start: 8px;
$data_set_1: 46, 12, 23, 11, 38, 48, 19;
$data_set_2: 61, 50, 65, 55, 61, 74, 64;
$legend_bar_length: 6px;

.frame {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 400px;
  height: 400px;
  margin-top: -200px;
  margin-left: -200px;
  border-radius: 2px;
  box-shadow: 4px 8px 16px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  background: $teal;
  color: $white;
  font-family: "Open Sans", Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.card {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: $white;
  height: 220px;
  width: 280px;
  border-radius: 3px;
  box-shadow: 10px 10px 15px 0 rgba(0, 0, 0, 0.3);

  &__header {
    position: relative;
    background: $orange;
    height: 60px;
    width: 100%;

    .lg {
      font-weight: 600;
      font-size: 14px;
      text-transform: uppercase;
      line-height: 1.5rem;
    }

    .sm {
      font-weight: 400;
      font-size: 11px;
      line-height: 1.5rem;
    }

    .left {
      position: absolute;
      left: 15px;
    }

    .right {
      position: absolute;
      right: 15px;
    }

    .title {
      top: 10px;
    }

    .subtitle {
      top: 25px;
    }
  }
  &__body {
    .graph {
      position: relative;
      height: 81px;
      width: 260px;
      margin: 0 auto;

      &__legend {
        text-align: right;
        padding: 0.6rem 0;

        span {
          position: relative;
          font-size: 0.6rem;
          color: $gray;
          line-height: 1.2rem;
          padding: 0 0.6rem 0 1.2rem;
        }

        span:before {
          position: absolute;
          left: 1px;
          top: 6px;
          width: 11px;
          height: 3px;
          border-radius: 3px;
          content: "";
        }

        .red:before {
          background: $red;
        }

        .blue:before {
          background: $blue;
        }
      }
      .line {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: #f2f2f2;
      }

      .line-md {
        @extend .line;
        top: 40px;
      }

      .line-bt {
        @extend .line;
        top: inherit;
        bottom: 0;
      }

      .data {
        svg {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
        }

        polyline {
          fill: none;
          stroke-width: 2;
          stroke-linecap: round;
        }

        &.red polyline {
          stroke: $red;
        }

        &.blue polyline {
          stroke: $blue;
        }

        .tooltip {
          position: absolute;
          bottom: 1.2rem;
          left: 50%;
          transform: translate3d(-50%, 10px, 0);
          font-weight: 600;
          font-size: 0.8rem;
          line-height: 1.1rem;
          color: $white;
          padding: 0.5rem;
          border-radius: 3px;
          visibility: hidden;
          opacity: 0;
          transition: transform 0.4s ease-out 0s, opacity 0.4s ease-out 0s;

          &:after {
            position: absolute;
            display: block;
            content: "";
            height: 6px;
            width: 6px;
            left: 50%;
            margin-left: -3px;
            bottom: -3px;
            transform: rotate(45deg);
          }
        }

        [class^="point-"] {
          position: absolute;
          width: 6px;
          height: 6px;
          border-radius: 3px;
          cursor: pointer;
          z-index: 10;

          &:hover .tooltip {
            visibility: visible;
            opacity: 1;
            transform: translate3d(-50%, 0, 0);
          }
        }

        &.red {
          [class^="point-"],
          .tooltip,
          .tooltip:after {
            background: $red;
          }

          @for $i from 1 through 7 {
            .point-#{$i} {
              left: $start + ($i - 1) * $step;
              top: nth($data_set_1, $i) - 3px;
            }
          }
        }

        &.blue {
          [class^="point-"],
          .tooltip,
          .tooltip:after {
            background: $blue;
          }

          @for $i from 1 through 7 {
            .point-#{$i} {
              left: $start + ($i - 1) * $step;
              top: nth($data_set_2, $i) - 3px;
            }
          }
        }
      }

      &__labels {
        width: 280px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;

        .label {
          width: (100% / 7);
          font-size: 9px;
          color: $gray;
          line-height: 1.5rem;
          text-transform: uppercase;
          text-align: center;
        }
      }
    }
  }
}
