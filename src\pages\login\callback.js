import React, { useEffect, useState } from 'react';
import { useOktaAuth } from '@okta/okta-react';
import { navigate } from 'gatsby';
import Layout from '../../components/layout';
import SEO from '../../components/seo';

// Create a client-only wrapper component
const LoginCallbackContent = () => {
  const { oktaAuth, authState } = useOktaAuth();
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!oktaAuth) return;

    console.log('Processing Auth0 callback...');

    const handleCallback = async () => {
      try {
        // Handle the redirect with the tokens from the URL
        await oktaAuth.handleLoginRedirect();
        console.log('Login successful, redirecting...');

        // Redirect to home page after successful login
        navigate('/');
      } catch (error) {
        console.error('Error handling login redirect:', error);
        setError(error.message || 'Unknown error during login process');
      }
    };

    // Only run the callback handler if not already authenticated
    if (!authState?.isAuthenticated) {
      handleCallback();
    } else {
      // Already authenticated, redirect to home
      navigate('/');
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [oktaAuth, authState]);

  if (error) {
    return (
      <div style={{ textAlign: 'center', margin: '2rem' }}>
        <h3 style={{ color: 'red' }}>Authentication Error</h3>
        <p style={{ color: 'red' }}>{error}</p>

        <div style={{ marginTop: '2rem' }}>
          <button
            onClick={() => navigate('/login')}
            style={{
              background: '#0277bd',
              color: 'white',
              border: 'none',
              padding: '0.5rem 1rem',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Return to Login
          </button>
        </div>
      </div>
    );
  }

  return null;
};

// Main component with SSR safety
const LoginCallback = () => {
  const isBrowser = typeof window !== 'undefined';

  return (
    <Layout title="Login Callback">
      <SEO title="Processing Login" />
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
        textAlign: 'center',
        padding: '2rem'
      }}>
        <h1>Processing Login</h1>
        <p>Please wait while we complete your login...</p>
        {/* Only render the LoginCallbackContent component on the client side */}
        {isBrowser && <LoginCallbackContent />}
        <div className="loading-spinner" style={{
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          width: '30px',
          height: '30px',
          animation: 'spin 2s linear infinite',
          marginTop: '1rem'
        }}></div>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    </Layout>
  );
};

export default LoginCallback;
