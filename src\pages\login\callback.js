import React, { useEffect } from 'react';
import { useOktaAuth } from '@okta/okta-react';
import Layout from '../../components/layout';
import SEO from '../../components/seo';

const LoginCallback = () => {
  const { oktaAuth, authState } = useOktaAuth();

  useEffect(() => {
    if (!authState?.isAuthenticated) {
      // Parse the tokens from the URL
      oktaAuth.handleLoginRedirect().catch(error => {
        console.error('Error handling login redirect:', error);
      });
    }
  }, [oktaAuth, authState]);

  return (
    <Layout title="Login Callback">
      <SEO title="Processing Login" />
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
        textAlign: 'center',
        padding: '2rem'
      }}>
        <h1>Processing Login</h1>
        <p>Please wait while we complete your login...</p>
        <div className="loading-spinner" style={{
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          width: '30px',
          height: '30px',
          animation: 'spin 2s linear infinite',
          marginTop: '1rem'
        }}></div>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    </Layout>
  );
};

export default LoginCallback;
