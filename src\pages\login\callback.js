import React, { useEffect, useState } from 'react';
import { navigate } from 'gatsby';
import Layout from '../../components/layout';
import SEO from '../../components/seo';
import { handleAuth0Callback, isAuthenticated } from '../../auth/auth0Service';

// Create a client-only wrapper component
const LoginCallbackContent = () => {
  const [error, setError] = useState(null);
  const [processing, setProcessing] = useState(true);

  useEffect(() => {
    console.log('Processing Auth0 callback...');

    const processCallback = async () => {
      try {
        // If already authenticated, redirect to home
        if (isAuthenticated()) {
          console.log('Already authenticated, redirecting to home');
          navigate('/');
          return;
        }

        // Handle the Auth0 callback
        const result = await handleAuth0Callback();

        if (result.success) {
          console.log('Login successful, redirecting...');
          // Redirect to home page after successful login
          navigate('/');
        } else {
          console.error('Error handling Auth0 callback:', result.error);
          setError(result.error);
        }
      } catch (error) {
        console.error('Unexpected error in callback:', error);
        setError(error.message || 'An unexpected error occurred');
      } finally {
        setProcessing(false);
      }
    };

    processCallback();
  }, []);

  if (error) {
    return (
      <div style={{ textAlign: 'center', margin: '2rem' }}>
        <h3 style={{ color: 'red' }}>Authentication Error</h3>
        <p style={{ color: 'red' }}>{error}</p>

        <div style={{ marginTop: '2rem' }}>
          <button
            onClick={() => navigate('/manual-login')}
            style={{
              background: '#0277bd',
              color: 'white',
              border: 'none',
              padding: '0.5rem 1rem',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (processing) {
    return (
      <div style={{ textAlign: 'center', margin: '2rem' }}>
        <p>Processing authentication...</p>
        <div className="loading-spinner" style={{
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          width: '30px',
          height: '30px',
          animation: 'spin 2s linear infinite',
          margin: '1rem auto'
        }}></div>
      </div>
    );
  }

  return null;
};

// Main component with SSR safety
const LoginCallback = () => {
  const isBrowser = typeof window !== 'undefined';

  return (
    <Layout title="Login Callback">
      <SEO title="Processing Login" />
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
        textAlign: 'center',
        padding: '2rem'
      }}>
        <h1>Processing Login</h1>
        <p>Please wait while we complete your login...</p>
        {/* Only render the LoginCallbackContent component on the client side */}
        {isBrowser && <LoginCallbackContent />}
        <div className="loading-spinner" style={{
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          width: '30px',
          height: '30px',
          animation: 'spin 2s linear infinite',
          marginTop: '1rem'
        }}></div>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    </Layout>
  );
};

export default LoginCallback;
