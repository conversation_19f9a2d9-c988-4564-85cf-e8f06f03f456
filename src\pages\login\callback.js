import React, { useEffect, useState } from 'react';
import { useOktaAuth } from '@okta/okta-react';
import { navigate } from 'gatsby';
import Layout from '../../components/layout';
import SEO from '../../components/seo';

// Create a client-only wrapper component
const LoginCallbackContent = () => {
  const { oktaAuth, authState } = useOktaAuth();
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!oktaAuth) return;

    console.log('Processing Okta callback...');

    const handleCallback = async () => {
      try {
        // Try to get the current URL parameters
        const url = window.location.href;
        const hasAuthParams = url.includes('?code=') || url.includes('&code=');

        if (hasAuthParams) {
          console.log('Auth code detected in URL, processing...');

          // If we have auth params in the URL but no transaction in storage
          if (url.includes('state=')) {
            const stateMatch = url.match(/state=([^&]*)/);
            if (stateMatch && stateMatch[1]) {
              console.log('Found state parameter, attempting to restore transaction');
              // We could try to manually restore the transaction here if needed
            }
          }

          // Handle the redirect with the tokens from the URL
          await oktaAuth.handleLoginRedirect();

          console.log('Login successful, redirecting...');
          // Get the return URL from localStorage or default to home
          const returnUrl = localStorage.getItem('okta-return-url') || '/';
          localStorage.removeItem('okta-return-url'); // Clean up

          // Redirect to home page after successful login
          navigate(returnUrl);
        } else {
          // No auth params in URL, redirect to login
          console.log('No auth parameters found in URL, redirecting to login');
          navigate('/login');
        }
      } catch (error) {
        console.error('Error handling login redirect:', error);

        // Special handling for the "Unable to retrieve OAuth redirect params from storage" error
        if (error.message && error.message.includes('Unable to retrieve OAuth redirect params from storage')) {
          setError('Authentication session expired or was lost. Please try logging in again.');
        } else {
          setError(error.message || 'Unknown error during login process');
        }
      }
    };

    // Only run the callback handler if not already authenticated
    if (!authState?.isAuthenticated) {
      handleCallback();
    } else {
      // Already authenticated, redirect to home
      navigate('/');
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [oktaAuth, authState]);

  if (error) {
    return (
      <div style={{ color: 'red', textAlign: 'center', margin: '2rem' }}>
        <h3>Authentication Error</h3>
        <p>{error}</p>
        <p>
          <button
            onClick={() => navigate('/login')}
            style={{
              background: '#0277bd',
              color: 'white',
              border: 'none',
              padding: '0.5rem 1rem',
              borderRadius: '4px',
              cursor: 'pointer',
              marginTop: '1rem'
            }}
          >
            Try Again
          </button>
        </p>
      </div>
    );
  }

  return null;
};

// Main component with SSR safety
const LoginCallback = () => {
  const isBrowser = typeof window !== 'undefined';


  return (
    <Layout title="Login Callback">
      <SEO title="Processing Login" />
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
        textAlign: 'center',
        padding: '2rem'
      }}>
        <h1>Processing Login</h1>
        <p>Please wait while we complete your login...</p>
        {/* Only render the LoginCallbackContent component on the client side */}
        {isBrowser && <LoginCallbackContent />}
        <div className="loading-spinner" style={{
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          width: '30px',
          height: '30px',
          animation: 'spin 2s linear infinite',
          marginTop: '1rem'
        }}></div>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    </Layout>
  );
};

export default LoginCallback;
