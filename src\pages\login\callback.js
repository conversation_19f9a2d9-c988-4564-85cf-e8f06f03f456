import React, { useEffect, useState } from 'react';
import { useOktaAuth } from '@okta/okta-react';
import { navigate } from 'gatsby';
import Layout from '../../components/layout';
import SEO from '../../components/seo';

// Create a client-only wrapper component
const LoginCallbackContent = () => {
  const { oktaAuth, authState } = useOktaAuth();
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!oktaAuth) return;

    console.log('Processing Auth0 callback...');

    const handleCallback = async () => {
      try {
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');
        const error = urlParams.get('error');
        const errorDescription = urlParams.get('error_description');

        console.log('URL parameters:', { code: code ? 'present' : 'missing', state, error, errorDescription });

        // Check for Auth0 errors
        if (error) {
          console.error('Auth0 returned an error:', error, errorDescription);
          setError(`Auth0 error: ${error}${errorDescription ? ': ' + errorDescription : ''}`);
          return;
        }

        // Check if we have an authorization code
        if (!code) {
          console.error('No authorization code found in URL');
          setError('No authorization code found. Please try logging in again.');
          return;
        }

        // Check if this is a direct login (from our direct-login.js page)
        const storedState = localStorage.getItem('auth0-state');
        const isDirectLogin = state && storedState && state === storedState;

        if (isDirectLogin) {
          console.log('Processing direct login callback');
          // For direct login, we'll handle the token exchange manually
          try {
            // We'll use the Okta SDK to handle the token exchange
            await oktaAuth.token.exchangeCodeForTokens({
              code,
              state,
              codeVerifier: localStorage.getItem('auth0-code-verifier') || undefined
            });

            console.log('Direct login successful');
            const returnUrl = localStorage.getItem('okta-return-url') || '/';
            localStorage.removeItem('okta-return-url');
            localStorage.removeItem('auth0-state');
            localStorage.removeItem('auth0-code-verifier');

            navigate(returnUrl);
          } catch (tokenError) {
            console.error('Error exchanging code for tokens:', tokenError);
            setError('Error completing authentication. Please try again.');
          }
        } else {
          // Standard Okta SDK flow
          console.log('Processing standard Okta callback');
          try {
            // Handle the redirect with the tokens from the URL
            await oktaAuth.handleLoginRedirect();

            console.log('Login successful, redirecting...');
            // Get the return URL from localStorage or default to home
            const returnUrl = localStorage.getItem('okta-return-url') || '/';
            localStorage.removeItem('okta-return-url'); // Clean up

            // Redirect to home page after successful login
            navigate(returnUrl);
          } catch (oktaError) {
            console.error('Error handling Okta login redirect:', oktaError);

            // Special handling for the "Unable to retrieve OAuth redirect params from storage" error
            if (oktaError.message && oktaError.message.includes('Unable to retrieve OAuth redirect params from storage')) {
              setError('Authentication session expired or was lost. Please try the direct login instead.');
            } else {
              setError(oktaError.message || 'Unknown error during login process');
            }
          }
        }
      } catch (error) {
        console.error('General error in callback handling:', error);
        setError('An unexpected error occurred. Please try again.');
      }
    };

    // Only run the callback handler if not already authenticated
    if (!authState?.isAuthenticated) {
      handleCallback();
    } else {
      // Already authenticated, redirect to home
      navigate('/');
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [oktaAuth, authState]);

  if (error) {
    return (
      <div style={{ textAlign: 'center', margin: '2rem' }}>
        <h3 style={{ color: 'red' }}>Authentication Error</h3>
        <p style={{ color: 'red' }}>{error}</p>

        <div style={{ marginTop: '2rem' }}>
          <p>Please try one of these options:</p>

          <div style={{ display: 'flex', justifyContent: 'center', gap: '1rem', flexWrap: 'wrap' }}>
            <button
              onClick={() => navigate('/login')}
              style={{
                background: '#0277bd',
                color: 'white',
                border: 'none',
                padding: '0.5rem 1rem',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Standard Login
            </button>

            <button
              onClick={() => navigate('/direct-login')}
              style={{
                background: '#2e7d32',
                color: 'white',
                border: 'none',
                padding: '0.5rem 1rem',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Direct Auth0 Login
            </button>

            <button
              onClick={() => {
                // Clear all auth-related storage
                if (window.localStorage) {
                  Object.keys(localStorage).forEach(key => {
                    if (key.startsWith('okta-') || key.startsWith('auth0-')) {
                      localStorage.removeItem(key);
                    }
                  });
                }
                navigate('/login');
              }}
              style={{
                background: '#d32f2f',
                color: 'white',
                border: 'none',
                padding: '0.5rem 1rem',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Clear Storage & Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

// Main component with SSR safety
const LoginCallback = () => {
  const isBrowser = typeof window !== 'undefined';


  return (
    <Layout title="Login Callback">
      <SEO title="Processing Login" />
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
        textAlign: 'center',
        padding: '2rem'
      }}>
        <h1>Processing Login</h1>
        <p>Please wait while we complete your login...</p>
        {/* Only render the LoginCallbackContent component on the client side */}
        {isBrowser && <LoginCallbackContent />}
        <div className="loading-spinner" style={{
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          width: '30px',
          height: '30px',
          animation: 'spin 2s linear infinite',
          marginTop: '1rem'
        }}></div>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    </Layout>
  );
};

export default LoginCallback;
