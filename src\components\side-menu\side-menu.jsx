import React, { useState, useEffect, useCallback } from "react"
import { useStaticQuery, graphql, Link } from "gatsby"
import DateRangeSelector from "../date-range-selector/date-range-selector"
import PaperClip from "../paper-clip/paper-clip"
import "./side-menu.css"

// Create a client-only component for the side menu content
const SideMenuContent = ({ currentSlug }) => {
  // Use the query in a component that only renders on the client
  const data = useStaticQuery(graphql`
    query SideMenuPosts {
      allMdx(
        sort: { fields: [frontmatter___date], order: DESC }
      ) {
        edges {
          node {
            fields {
              slug
            }
            frontmatter {
              title
              date
            }
          }
        }
      }
    }
  `);

  const POSTS_PER_PAGE = 5;
  const allPosts = data.allMdx.edges;
  const [filteredPosts, setFilteredPosts] = useState(allPosts);
  const [currentPage, setCurrentPage] = useState(1);
  const [dateRange, setDateRange] = useState({
    start: new Date(2020, 0, 1), // Default start date
    end: new Date() // Default end date (today)
  });

  // Get the earliest post date for the date range selector
  const earliestPostDate = allPosts.length > 0
    ? new Date(allPosts[allPosts.length - 1].node.frontmatter.date)
    : new Date(2020, 0, 1);

  // Filter posts by date range
  const filterPostsByDate = useCallback((posts, range) => {
    return posts.filter(post => {
      const postDate = new Date(post.node.frontmatter.date);
      return postDate >= range.start && postDate <= range.end;
    });
  }, []);

  // Handle date range change
  const handleDateRangeChange = (newRange) => {
    setDateRange(newRange);
    setFilteredPosts(filterPostsByDate(allPosts, newRange));
    setCurrentPage(1); // Reset to first page when filter changes
  };

  // Calculate pagination
  const totalPages = Math.ceil(filteredPosts.length / POSTS_PER_PAGE);
  const startIndex = (currentPage - 1) * POSTS_PER_PAGE;
  const endIndex = startIndex + POSTS_PER_PAGE;
  const currentPosts = filteredPosts.slice(startIndex, endIndex);

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="side-menu">
      <div className="side-menu-header">
        <h3>Recent Posts</h3>
        <DateRangeSelector
          earliestDate={earliestPostDate}
          initialDateRange={dateRange}
          onDateRangeChange={handleDateRangeChange}
        />
      </div>
      <ul className="post-list">
        {currentPosts.map(({ node }) => {
          const isCurrentPost = node.fields.slug === currentSlug;
          return (
            <li key={node.fields.slug} className={isCurrentPost ? 'current-post' : ''}>
              <Link to={node.fields.slug}>
                <div className="post-title">
                  {isCurrentPost && <PaperClip />}
                  <span>{node.frontmatter.title}</span>
                </div>
                <div className="post-date">{formatDate(node.frontmatter.date)}</div>
              </Link>
            </li>
          );
        })}
      </ul>
      {totalPages > 1 && (
        <div className="pagination">
          {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={page === currentPage ? 'active' : ''}
            >
              {page}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

// Main component that handles SSR
const SideMenu = ({ currentSlug }) => {
  const isBrowser = typeof window !== 'undefined';

  // If we're in the browser, render the client-side component with all the functionality
  if (isBrowser) {
    return <SideMenuContent currentSlug={currentSlug} />;
  }

  // For SSR, render a simplified version
  return (
    <div className="side-menu">
      <div className="side-menu-header">
        <h3>Recent Posts</h3>
      </div>
      <div className="loading">Loading posts...</div>
    </div>
  );


}

export default SideMenu
