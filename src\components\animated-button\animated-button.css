.center {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.button {
    position: relative;
    cursor: pointer;
    border-radius: 8px;
    color: var(--color-text-secondary)
}

.button span {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    text-align: center;
    line-height: 48px;
    text-transform: uppercase;
    font-size: 18px;
    letter-spacing: 1px;
    font-weight: 300;
}

.button .border {
    fill: none;
    stroke-width: 1;
    box-shadow: var(--shadow-small);
}

.button .bg-line {
    fill: var(--color-lightgrey);
    stroke: var(--color-white);
    transition: all 0.8s ease-in-out;
}

.button .hl-line {
    stroke: var(--color-secondary);
    stroke-dasharray: 40 480;
    stroke-dashoffset: 40;
    transition: all 0.8s ease-in-out;
}

.button:hover .bg-line {
    fill: var(--color-lightgrey);
}

.button:hover .hl-line {
    stroke-dashoffset: -480;
}