import React from "react"
import { <PERSON> } from "gatsby"
import "./user-profile.css"

const UserProfile = ({ isLoggedIn, username }) => {
  return (
    <div className="user-profile">
      {isLoggedIn ? (
        <div className="user-profile__logged-in">
          <div className="user-profile__avatar">
            <span className="user-profile__initials">
              {username ? username.charAt(0).toUpperCase() : "U"}
            </span>
          </div>
          <div className="user-profile__dropdown">
            <Link to="/profile" className="user-profile__link">Profile</Link>
            <Link to="/logout" className="user-profile__link">Sign Out</Link>
          </div>
        </div>
      ) : (
        <div className="user-profile__auth-links">
          <Link to="/login" className="user-profile__link">Sign In</Link>
          <span className="user-profile__separator">|</span>
          <Link to="/register" className="user-profile__link">Sign Up</Link>
        </div>
      )}
    </div>
  )
}

export default UserProfile