import React from 'react';
import { Security } from '@okta/okta-react';
import { OktaAuth } from '@okta/okta-auth-js';
import { navigate } from 'gatsby';

// Import the auth config from the root directory
const authConfig = {
  clientId: process.env.GATSBY_OKTA_CLIENT_ID || "BDEQBKCbIxFjpcDfqK5PWeI461jKCQ5m",
  // Update redirectUri to match what's configured in Okta
  redirectUri: typeof window !== 'undefined' ? `${window.location.origin}/login/callback` : '',
  scopes: ['openid', 'profile', 'email'],
  // Use Auth0 issuer format - make sure to include trailing slash
  issuer: process.env.GATSBY_OKTA_ISSUER || "https://dev-h4miq62iedog2a6m.us.auth0.com/",
  // Auth0-specific configuration
  auth0Domain: "dev-h4miq62iedog2a6m.us.auth0.com",
  // For Auth0, we need to use the OAuth 2.0 /authorize endpoint
  authorizeUrl: "https://dev-h4miq62iedog2a6m.us.auth0.com/authorize",
  // For Auth0, we need to use the OAuth 2.0 /token endpoint
  tokenUrl: "https://dev-h4miq62iedog2a6m.us.auth0.com/oauth/token",
  // For Auth0, we need to use the OAuth 2.0 /userinfo endpoint
  userinfoUrl: "https://dev-h4miq62iedog2a6m.us.auth0.com/userinfo"
};

// Initialize the Okta Auth client with Auth0 configuration
const oktaAuth = typeof window !== 'undefined'
  ? new OktaAuth({
      issuer: authConfig.issuer,
      clientId: authConfig.clientId,
      redirectUri: authConfig.redirectUri,
      scopes: authConfig.scopes,
      responseType: 'code',
      pkce: true,
      cookies: {
        secure: false // Allow non-secure cookies in development
      },
      // Use localStorage instead of sessionStorage for better persistence
      storageManager: {
        token: {
          storageTypes: ['localStorage', 'cookie']
        },
        cache: {
          storageTypes: ['localStorage', 'cookie']
        },
        transaction: {
          storageTypes: ['localStorage', 'cookie']
        }
      },
      // Auth0-specific endpoints
      authorizeUrl: authConfig.authorizeUrl,
      tokenUrl: authConfig.tokenUrl,
      userinfoUrl: authConfig.userinfoUrl,
      // For Auth0, we need to set these options
      testing: {
        disableHttpsCheck: process.env.NODE_ENV !== 'production'
      }
    })
  : null;

// Log configuration in development for debugging
if (typeof window !== 'undefined' && process.env.NODE_ENV !== 'production') {
  console.log('Okta Auth Configuration:', {
    issuer: authConfig.issuer,
    clientId: authConfig.clientId,
    redirectUri: authConfig.redirectUri,
    scopes: authConfig.scopes,
    // Auth0-specific endpoints
    authorizeUrl: authConfig.authorizeUrl,
    tokenUrl: authConfig.tokenUrl,
    userinfoUrl: authConfig.userinfoUrl
  });

  // Log the full Okta configuration
  console.log('Full Okta Auth instance:', oktaAuth);
}

// Provider component
export const OktaProvider = ({ children }) => {
  // Function to restore the original URI after login
  const restoreOriginalUri = async (_oktaAuth, originalUri) => {
    navigate(originalUri || '/');
  };

  // If window is not defined (during SSR), return children without Okta
  if (typeof window === 'undefined' || !oktaAuth) {
    return <>{children}</>;
  }

  return (
    <Security oktaAuth={oktaAuth} restoreOriginalUri={restoreOriginalUri}>
      {children}
    </Security>
  );
};

// Export the oktaAuth instance
export { oktaAuth };
