import React from 'react';
import { Security } from '@okta/okta-react';
import { OktaA<PERSON> } from '@okta/okta-auth-js';
import { navigate } from 'gatsby';

// Import the auth config from the root directory
const authConfig = {
  domain: process.env.GATSBY_OKTA_DOMAIN || "dev-h4miq62iedog2a6m.us.auth0.com",
  clientId: process.env.GATSBY_OKTA_CLIENT_ID || "BDEQBKCbIxFjpcDfqK5PWeI461jKCQ5m",
  redirectUri: typeof window !== 'undefined' ? `${window.location.origin}/callback` : '',
  scopes: ['openid', 'profile', 'email'],
  pkce: true,
  issuer: process.env.GATSBY_OKTA_ISSUER || "https://dev-h4miq62iedog2a6m.us.auth0.com/oauth2/default"
};

// Initialize the Okta Auth client
const oktaAuth = typeof window !== 'undefined'
  ? new OktaAuth({
      issuer: authConfig.issuer,
      clientId: authConfig.clientId,
      redirectUri: authConfig.redirectUri,
      scopes: authConfig.scopes,
    })
  : null;

// Create a context for Okta auth
export const OktaContext = React.createContext(null);

// Provider component
export const OktaProvider = ({ children }) => {
  // Function to restore the original URI after login
  const restoreOriginalUri = async (_oktaAuth, originalUri) => {
    navigate(originalUri || '/');
  };

  // If window is not defined (during SSR), return children without Okta
  if (typeof window === 'undefined' || !oktaAuth) {
    return <>{children}</>;
  }

  return (
    <Security oktaAuth={oktaAuth} restoreOriginalUri={restoreOriginalUri}>
      {children}
    </Security>
  );
};

// Custom hook to access Okta auth
export const useOktaAuth = () => {
  const context = React.useContext(OktaContext);
  if (!context) {
    throw new Error('useOktaAuth must be used within an OktaProvider');
  }
  return context;
}
export { OktaProvider, oktaAuth };
