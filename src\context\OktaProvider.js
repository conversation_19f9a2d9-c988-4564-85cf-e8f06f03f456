import React from 'react';
import { Security } from '@okta/okta-react';
import { OktaAuth } from '@okta/okta-auth-js';
import { navigate } from 'gatsby';

// Import the auth config from the root directory
const authConfig = {
  domain: process.env.GATSBY_OKTA_DOMAIN || "dev-h4miq62iedog2a6m.us.auth0.com",
  clientId: process.env.GATSBY_OKTA_CLIENT_ID || "BDEQBKCbIxFjpcDfqK5PWeI461jKCQ5m",
  // Update redirectUri to match what's configured in Okta
  redirectUri: typeof window !== 'undefined' ? `${window.location.origin}/login/callback` : '',
  scopes: ['openid', 'profile', 'email'],
  pkce: true,
  issuer: process.env.GATSBY_OKTA_ISSUER || "https://dev-h4miq62iedog2a6m.us.auth0.com/oauth2/default"
};

// Initialize the Okta Auth client
const oktaAuth = typeof window !== 'undefined'
  ? new OktaAuth({
      issuer: authConfig.issuer,
      clientId: authConfig.clientId,
      redirectUri: authConfig.redirectUri,
      scopes: authConfig.scopes,
      responseType: 'code',
      pkce: true,
      tokenManager: {
        autoRenew: true,
        secure: true
      }
    })
  : null;

// Log configuration in development for debugging
if (typeof window !== 'undefined' && process.env.NODE_ENV !== 'production') {
  console.log('Okta Auth Configuration:', {
    issuer: authConfig.issuer,
    clientId: authConfig.clientId,
    redirectUri: authConfig.redirectUri,
    scopes: authConfig.scopes
  });
}

// Provider component
export const OktaProvider = ({ children }) => {
  // Function to restore the original URI after login
  const restoreOriginalUri = async (_oktaAuth, originalUri) => {
    navigate(originalUri || '/');
  };

  // If window is not defined (during SSR), return children without Okta
  if (typeof window === 'undefined' || !oktaAuth) {
    return <>{children}</>;
  }

  return (
    <Security oktaAuth={oktaAuth} restoreOriginalUri={restoreOriginalUri}>
      {children}
    </Security>
  );
};

// Export the oktaAuth instance
export { oktaAuth };
