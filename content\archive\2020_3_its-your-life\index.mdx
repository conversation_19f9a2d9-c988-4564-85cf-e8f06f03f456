---
title: It's Your Life
date: "2020-09-11T19:12:03.284Z"
description: "Make no mistake, it is your career, and more importantly, It's Your Life. You own it."
---

![Happy](./happy.jpg)

"I am not in this world to live up to your expectations<br/>
and you are not in this world to live up to mine"<br/>
<PERSON>

"The Pragmatic Programmer" by <PERSON> and <PERSON> was the best technical
book I ever read in my life. I received it as a Christmas gift from my supervisor. This
book caught me in the first chapter and I ended up spending my entire holiday vacation
reading it page after page and could not stop. That was the book I could hardly put down.
I was in a fancy spa hotel with a huge water park, heated outdoor pool, and many
cool entertaiment activities, but my face displayed such level of excitement that a
number of people stopped and asked for the name of the book.

Since then I was re-reading some chapters over and over again finding a valuable
insight to a bigger context of various situations I've been at work. Yes, this
book is truly about me and chances are, if you are reading my blog, this book is
about you too.

As software developers we have skills that are in high demand, paid well and
allow for a lot of flexibilities like working from literally anywhere. Yet there are
many devs that are not fully happy with what they have and do nothing to make
things better. The quote below discusses this at the right angle - the angle of
taking a responsibility for own life.

> It is your life. You own it. You run it. You create it.
>
> Many developers we talk to are frustrated. Their concerns are varied. Some
> feel they're stagnating in their job, others that technology has passed them
> by. Folks feel they are under appreciated, or underpaid, or that their teams
> are toxic. Maybe they want to move to Asia, or Europe, or work from home.
>
> And the answer we give is always the same.
>
> "Why can't you change it?"
>
> Software development must appear close to the top of any list of careers where
> you have control. Our skills are in demand, our knowledge crosses geographic
> boundaries, we can work remotely. We're paid well. We really can do just about
> anything we want.
>
> But, for some reason, developers seem to resist change. They hunker down,
> and hope things will get better. They look on, passively, as their skills become
> dated and complain that their companies don't train them. They look at ads
> for exotic locations on the bus, then step off into the chilling rain and trudge
> into work.
>
> So here's the most important tip in the book.
>
> <strong> You Have Agency</strong>
>
> Does your work environment suck? Is your job boring? Try to fix it. But don't
> try forever. As Nartin Fowler says, "you can change your organization or change
> your organization."([Source](https://wiki.c2.com/?ChangeYourOrganization))
>
> If technology seems to be passing you by, make time (in your own time) to
> study new stuff that looks interesting. You're investing in yourself, so doing
> it while your're off the clock is only reasonable.
>
> Want to work remotely? Have you asked? If they say no, then find someone
> who says yes.
>
> This industry gives you a remarkable set of opportunities. Be proactive, and
> take them.
>
> Thomas, D., Hunt, A. (2020). The Pragmatic Programmer

All I can add is that we, developers, have all resources to reach the full potential
and ultimate happiness. All it takes just to get responsible for own life.
