import React from "react";
import { Auth0Provider } from "@auth0/auth0-react";
import { navigate } from "gatsby";

const AUTH_DOMAIN = process.env.GATSBY_OKTA_DOMAIN;
const AUTH_CLIENT_ID = process.env.GATSBY_OKTA_CLIENT_ID;
const AUTH_AUDIENCE = process.env.GATSBY_OKTA_ISSUER;

const Auth0ProviderWithHistory = ({ children }) => {
    console.log('Auth0ProviderWithHistory called - temporarily bypassing Auth0');
    // Temporarily bypass Auth0 to debug white screen issue
    return children;
};

export default Auth0ProviderWithHistory;
