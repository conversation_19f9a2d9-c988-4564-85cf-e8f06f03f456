{"name": "gatsby-personal-starter-blog", "private": true, "description": "A personal blog starter for a blog powered by Netlify CMS, based on the default blog starter", "version": "2.0.0", "author": "<PERSON><PERSON> <<EMAIL>>", "dependencies": {"@fortawesome/fontawesome-free": "^5.14.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@mdx-js/mdx": "^1.5.1", "@mdx-js/react": "^1.5.1", "@okta/okta-auth-js": "^6.9.0", "@okta/okta-react": "^6.10.0", "@reach/router": "^1.3.4", "babel-plugin-styled-components": "^1.10.0", "disqus-react": "^1.1.5", "dotenv": "^8.2.0", "gatsby": "^2.24.47", "gatsby-image": "^2.0.39", "gatsby-plugin-feed-mdx": "^1.0.0", "gatsby-plugin-google-analytics": "^2.0.18", "gatsby-plugin-google-gtag": "^2.4.0", "gatsby-plugin-local-search": "^1.1.1", "gatsby-plugin-manifest": "^2.0.29", "gatsby-plugin-mdx": "^1.2.14", "gatsby-plugin-netlify-cms": "^4.3.7", "gatsby-plugin-offline": "^3.2.23", "gatsby-plugin-react-helmet": "^3.0.12", "gatsby-plugin-sharp": "^2.0.35", "gatsby-plugin-styled-components": "^3.0.7", "gatsby-plugin-typography": "^2.2.13", "gatsby-remark-copy-linked-files": "^2.0.11", "gatsby-remark-images": "^3.3.9", "gatsby-remark-responsive-iframe": "^2.1.1", "gatsby-remark-smartypants": "^2.0.9", "gatsby-remark-vscode": "^2.1.2", "gatsby-source-filesystem": "^2.0.29", "gatsby-transformer-sharp": "^2.1.18", "imagemin-pngquant": "^5.0.1", "install": "^0.13.0", "jquery": "^3.5.1", "netlify-cms-app": "^2.9.1", "npm": "^11.0.0", "pngquant-bin": "^3.1.1", "query-string": "^6.13.1", "react": "^16.8.6", "react-dom": "^16.8.6", "react-helmet": "^5.2.0", "react-icons": "^4.12.0", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-switch": "^5.0.0", "react-typography": "^0.16.19", "react-use-flexsearch": "^0.1.1", "sharp": "^0.33.5", "styled-components": "^4.2.0", "tailwindcss": "^3.4.17", "three": "^0.120.1", "typeface-merriweather": "0.0.72", "typeface-montserrat": "0.0.54", "typeface-open-sans": "^1.1.13", "typography": "^0.16.19", "typography-theme-wordpress-2016": "^0.16.19"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-syntax-class-properties": "^7.12.13", "gatsby-cli": "^2.12.66", "gatsby-plugin-root-import": "^2.0.5", "prettier": "^1.17.0"}, "scripts": {"build": "gatsby clean && gatsby build", "develop": "gatsby develop", "format": "prettier --write src/**/*.{js,jsx}", "start": "npm run develop", "serve": "gatsby serve", "test": "echo \"Write tests! -> https://gatsby.dev/unit-testing\"", "api": "node server.js"}}