.side-menu {
	position: absolute;
	width: 264px;
	height: auto;
	background: color-mix(in srgb, var(--color-blue) 50%, transparent);
	/* Semi-transparent blue background */
	border-radius: 8px;
	z-index: calc(var(--z-modal) - 1);
	/* overflow-y: auto; */
	box-shadow: var(--shadow-small);
	backdrop-filter: blur(6px);
	box-shadow: 4px 0 10px rgba(0, 0, 0, 0.2);
	left: 4px;
}

.date-range {
	padding: 12px 16px;
	border-bottom: 1px solid var(--color-grey);
	font-size: var(--font-size-small);
	color: var(--color-text-muted);
	display: flex;
	align-items: center;
	justify-content: center;
	background: var(--color-bluegrey);
	gap: 8px;
}

.date-separator {
	margin: 0;
	color: var(--color-text-muted);
}

.side-menu ul {
	margin: 0;
	padding: 0;
}

.side-menu li {
	position: relative;
	color: var(--color-text-primary);
	margin: 0;
	padding-block: 12px;
	padding-inline: 16px 24px;
	list-style: none;
	font-size: calc(var(--font-size-small)*0.9);
	line-height: 1.4;
	cursor: pointer;
	transition: all 0.6s ease-in-out;
	border-bottom: 1px solid transparent;
	border-top: 1px solid var(--color-grey);
	background: linear-gradient(to bottom, #f0f5fa, #f0f5fa) no-repeat center bottom;
	background-size: 100% 1px;
	overflow: visible;
	background-color: #f8fafd; /* Even lighter blue background */
}

.side-menu li .paperclip {
	position: absolute;
	top: -15px;
	right: -5px;
	font-size: 1.2rem;
	transform: rotate(45deg);
	z-index: 10;
	pointer-events: none;
	color: var(--color-blue);
	opacity: 0;
	transition: opacity 0.3s ease;
}

.side-menu li:hover .paperclip {
	opacity: 0.7;
}

@keyframes paperclip-attach {
  0% {
    transform: translateX(-10px) rotate(-135deg);;
    opacity: 0;
  }
  50% {
    transform: translateX(-5px)rotate(-135deg);;
    opacity: 0.5;
  }
  100% {
    transform: translateX(-1px) rotate(-135deg);
    opacity: 1;
  }
}

@keyframes text-shift {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(1px);
  }
  100% {
    transform: translateX(4px);
  }
}

.side-menu li.current {
  position: relative;
  background-color: #e3f2fd;
  color: #1565c0;
  font-weight: 600;
  border-left: 4px solid #42a5f5;
  font-size: calc(var(--font-size-small)*0.9);
  animation: here-am-i 0.5s ease-in-out;
}

.side-menu li.current a {
  animation: text-shift 0.6s ease-in-out forwards;
}

.side-menu li.current .paperclip {
  top: -15px;
  right: -5px;
  opacity: 1;
  transform: rotate(45deg);
  color: var(--color-blue);
  animation: paperclip-attach 0.6s ease-in-out forwards;
}

.side-menu li .paperclip {
  position: absolute;
  top: -15px;
  right: -5px;
  font-size: 1.2rem;
  transform: rotate(45deg);
  z-index: 10;
  pointer-events: none;
  color: var(--color-blue);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.side-menu li:last-child {
	border-bottom: none;
	/* Remove border from last item */
}

.side-menu li:hover {
	background-color: #e8f0f8; /* Slightly darker on hover for contrast */
	transform: translateY(3px);
	transition: all 0.4s ease-in-out;
}

.side-menu li a {
	color: inherit;
	text-decoration: none;
	display: flex;
	align-items: center;
	gap: 8px;
}

.side-menu li span {
	color: var(--color-text-muted);
	width: 16px;
	text-align: center;
}

/* Custom scrollbar */
.side-menu::-webkit-scrollbar {
	width: 6px;
}

.side-menu::-webkit-scrollbar-track {
	background: var(--color-background-secondary);
}

.side-menu::-webkit-scrollbar-thumb {
	background: var(--color-lightgrey);
	border-radius: 4px;
}

.side-menu::-webkit-scrollbar-thumb:hover {
	background: var(--color-text-light);
}

/* @keyframes here-am-i {
	from {
		transform: translate3d(0, 20px, 0);
		opacity: 0;
	}

	to {
		transform: translate3d(0, 0, 0);
		opacity: 1;
	}
} */

.side-menu li.current {
	position: relative;
	background-color: #e3f2fd;
	color: #1565c0;
	font-weight: 600;
	border-left: 4px solid #42a5f5;
	font-size: calc(var(--font-size-small)*0.9);
	animation: here-am-i 0.5s ease-in-out;
	/* animation: here-am-i 0.5s ease-in-out; */
}

.side-menu li.current .paperclip {
	top: -4px;
	left: 0px;
	margin-left: 0px;
	/* font-size: 1.2rem; */
	transform: rotate(-135deg);
	z-index: 2;
	pointer-events: none;
	color: var(--color-blue);
}

.side-menu li:hover .paperclip {
	top: -4px;
	left: 0px;
	margin-left: 0px;
	/* font-size: 1.2rem; */
	transform: rotate(-135deg);
	z-index: 2;
	pointer-events: none;
	color: var(--color-blue);
}


/* Remove any conflicting paperclip styles from paper-clip.css */

.side-menu li.current:hover {
	transform: translateY(-3px);
	transition: all 0.4s ease-in-out;
}

.pagination {
	/* position: sticky; */
	bottom: 0;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px;
	background: var(--color-bluegrey);
	border-top: 1px solid var(--color-grey);
}

.pagination-arrow {
	background: none;
	border: none;
	cursor: pointer;
	padding: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.pagination-arrow:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

.pagination-arrow:not(:disabled):hover {
	transform: translateY(-1px);
}

.page-indicator {
	font-size: var(--font-size-small);
	color: var(--color-text-muted);
}

/* Reuse existing nav-arrow styles from your CSS */
.pagination .nav-arrow {
	width: 24px;
	height: 24px;
}

.no-posts {
	padding: 16px;
	text-align: center;
	color: var(--color-text-muted);
	font-size: var(--font-size-small);
}
