import React, { useEffect, useState } from 'react';
import { navigate } from 'gatsby';

// SecureRoute component that checks if user is authenticated
const SecureRoute = ({ component: Component, ...rest }) => {
  const [authenticated, setAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const isBrowser = typeof window !== 'undefined';

  useEffect(() => {
    if (!isBrowser) return;

    // Import auth service dynamically to avoid SSR issues
    import('../auth/auth0Service').then(({ isAuthenticated }) => {
      const isAuth = isAuthenticated();
      setAuthenticated(isAuth);
      setLoading(false);

      if (!isAuth) {
        navigate('/login');
      }
    });
  }, [isBrowser]);

  if (loading) {
    // Still loading auth state
    return <div>Loading authentication...</div>;
  }

  if (!authenticated) {
    // If not authenticated, we're redirecting
    return null;
  }

  // If authenticated, render the component
  return <Component {...rest} />;
};

export default SecureRoute;
