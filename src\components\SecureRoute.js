import React from 'react';
import { useOktaAuth } from '@okta/okta-react';
import { navigate } from 'gatsby';

// SecureRoute component that checks if user is authenticated
const SecureRoute = ({ component: Component, ...rest }) => {
  const { authState } = useOktaAuth();

  if (!authState) {
    // Still loading auth state
    return <div>Loading authentication...</div>;
  }

  if (!authState.isAuthenticated) {
    // If not authenticated, redirect to login
    if (typeof window !== 'undefined') {
      navigate('/login');
    }
    return null;
  }

  // If authenticated, render the component
  return <Component {...rest} />;
};

export default SecureRoute;
