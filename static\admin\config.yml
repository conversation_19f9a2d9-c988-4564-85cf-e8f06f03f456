backend:
  name: github
  repo: <PERSON><PERSON>-<PERSON><PERSON><PERSON>/my-blog

media_folder: content/assets
public_folder: ../assets

collections:
  - name: blog
    label: Blog
    folder: content/blog
    create: true
    fields:
      - { name: path, label: Path }
      - { name: date, label: Date, widget: date }
      - { name: title, label: Title }
      - { name: description, label: Description }
      - {
          name: visibility,
          label: Status,
          widget: select,
          options: ["public", "private", "draft"],
          default: "public"
        }
      - { name: body, label: Body, widget: markdown }
