---
title: "Three, Pt and more"
date: "2020-09-03T09:52:03.284Z"
description: Animation is now a organic part of the web. What are your favorite libraries?
---

### Pure CSS


<div id="animated_div">CSS</div>

Yes! CSS allows you to animate HTML elements without relying on JavaScript or Flash. Animations let elements 
transition gradually from one style to another, and you can modify as many CSS properties as you want, as many times
 as you need.
 You can change as many CSS properties you want, as many times you want.

<iframe
  height="400"
  style="width: 100%;"
  scrolling="no"
  title="Pure CSS Saturn Hula Hooping"
  src="https://codepen.io/jcoulterdesign/embed/BrdPaw?height=409&theme-id=light&default-tab=result"
  frameborder="no"
  loading="lazy"
  allowtransparency="true"
  allowfullscreen="true"
>
  See the Pen{" "}
  <a href="https://codepen.io/jcoulterdesign/pen/BrdPaw">
    Pure CSS Saturn Hula Hooping
  </a>{" "}
  by <PERSON> (
  <a href="https://codepen.io/jcoulterdesign">@jcoulterdesign</a>) on{" "}
  <a href="https://codepen.io">CodePen</a>.
</iframe>

CSS remains the most performant and cross-platform/browser-compatible way to animate website elements. The result? 
Scalable, fast-loading, and visually impressive effects. Check out [some amazing examples](https://webdesign.tutsplus.com/articles/pure-css-animation-inspiration-on-codepen--cms-30875) of pure CSS animations
 for inspiration.

### [Three](https://threejs.org/docs/index.html#manual/en/introduction/Creating-a-scene)

<iframe
height="400"
  style="width: 100%;"
  id="scene"
  src="https://threejs.org/docs/scenes/geometry-browser.html#TorusKnotGeometry"
>

</iframe>

Three.js is an open-source 3D JavaScript library with over 63K stars on GitHub. It supports Canvas, SVG, and WebGL
 renderers, enabling you to create interactive 3D animations directly in the browser using WebGL in an intuitive way.
  First introduced in April 2010, the library is actively developed by nearly 1,000 contributors. You can explore
   examples, documentation, and download the latest package on its [homepage](http://mrdoob.github.com/three.js).

import Card from "src/components/card"
import photo from "./ricardoCabello.jpg"
import wngan from "./wngan.png"
import airbnb from "./airbnb.png"
import Quote from "src/components/quote/quote";


 <Card photo={photo} author="Ricardo Cabello" repo="mrdoob/three.js" about="JavaScript 3D library." platform= "github.com" />





### [Pt](https://ptsjs.org/guide/get-started-0100)

<iframe
height="400"
  style="width: 100%;"
  id="scene"
  src="https://williamngan.github.io/pt/demo/index.html?name=triangle.incenter"
>
</iframe>

<Quote text="A line is a dot that went for a walk.
Drawing is taking a line for a walk."  author="Paul Klee"/>

The Pts library embodies this philosophy. Starting with a simple point, it builds concepts when positioned in space and transforms into meaning when shaped into forms. Space and form, in turn, create a context for the point. By treating code as a new medium, Pts automates processes, models abstractions, and designs interactions. Together, Point, Space, and Form orchestrated through Code produce stunning visualizations on a canvas.
 <Card photo={wngan} author="William Ngan" repo="williamngan/pts" about="A library for visualization and creative-coding." platform= "github.com" />

### [Lottie](https://github.com/airbnb/lottie-web) 

![Alt Text](https://miro.medium.com/max/800/1*qZMz2abxuSdvi6YQOFkLVg.gif)

Lottie is a mobile-friendly library for Web and iOS that renders Adobe After Effects animations exported as JSON files with Bodymovin. It supports Web, Android, iOS, and React Native, allowing animations to render natively across platforms.

 <Card photo={airbnb} author="Airbnb" repo="airbnb/lottie-web" about="Render After Effects animations natively on Web, Android and iOS, and React Native." platform= "github.com" />

### More...

Looking for more detailed effects like SVG drawing, scroll-triggered animations, hover effects, or type animations? Check out this
[article](https://blog.bitsrc.io/11-javascript-animation-libraries-for-2018-9d7ac93a2c59) featuring over 20 animation libraries. It also includes a clean and simple GitHub profile card design worth exploring!
