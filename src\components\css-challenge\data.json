[{"title": "zYqMrKR", "info": "Blobby", "description": "A mesmerizing shape morphing animation with synchronized sparkles, bounce effects, and geometric transformations."}, {"title": "dyMQGXw", "info": "Upload", "description": "A drag-and-drop file uploader with animated progress, syncing feedback, and a vibrant gradient background."}, {"title": "ExKOPyE", "info": "Byciclopter", "description": "A 3D flipping animation card with playful bike and helicopter motion, simulating dynamic environments on both sides."}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "info": "User Gallery", "description": "An interactive profile card with hover effects and a fullscreen animated detail view using smooth transitions and a custom close button."}, {"title": "jOqxRjg", "info": "<PERSON><PERSON><PERSON>", "description": "A tooltip-enhanced quote card with layered styling, smooth transitions, and a bold typographic design."}, {"title": "MWyVNgd", "info": "Walking Boots", "description": "Featuring an animated walk cycle, brought to life through swinging shoes and smooth keyframe motion on a radial gradient background."}, {"title": "dyMmvzZ", "info": "Watch.", "description": "Featuring a rotating circular pulse animation, synchronized heartbeat effect, and radial energy points—all crafted CSS and keyframes."}, {"title": "mdPxWWL", "info": "Weather.", "description": "Showcasing a serene nights, combining SCSS loops and keyframes to simulate a peaceful rainy weather forecast interface."}, {"title": "PoNQPBe", "info": "Metaballs.", "description": "Featuring Slim, filters and random math."}, {"title": "OJNzddg", "info": "Notification Panel.", "description": "Featuring a sliding notification panel and an animated search bar."}, {"title": "xxVprNr", "info": "Loading...", "description": "Featuring loading event animation with a 3D effect."}, {"title": "mdPpwgj", "info": "The Pyramide.", "description": "Featuring a continuous animation with a day time change effect."}, {"title": "bGpaRdK", "info": "<PERSON><PERSON>.", "description": "Featuring on click animation of a hamburger menu. Implemented with use of cubic-bezier for transition effect with variable speed."}, {"title": "oNxpzVV", "info": "Profile.", "description": "Featuring animated two circle image frame and hover effect on elements. Implemented with partially transparent border."}, {"title": "LYNepzy", "info": "Weekly Report.", "description": "Featuring animated tooltip on graph point hovering. Implemented with using sass array variables and loops."}]