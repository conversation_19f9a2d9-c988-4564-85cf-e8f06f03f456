.post {
    position: relative;
}

.post-nav__item {
    font-size: var(--font-size-small);
}

.panel {
    position: absolute;
    z-index: var(--z-modal);
    height: fit-content;
    width: 100%;
    background: var(--color-background-primary);
    border-radius: var(--border-radius-small);
    overflow: hidden;
    /* box-shadow: var(--shadow-large); */
    transition: all var(--duration-slow) var(--animation-timing-default);
    padding: 1rem;
}

body.show-nav .panel {
    transform: translate3d(250px, 0, 0);
    box-shadow: var(--shadow-large);
}

/* Blog post content text color */
.post-title {
    color: var(--color-text-primary);
}

.post-date {
    color: var(--color-text-secondary);
}

.post-content {
    color: var(--color-text-primary);
}

.post-content p {
    color: var(--color-text-primary);
}

.post-content h1,
.post-content h2,
.post-content h3,
.post-content h4,
.post-content h5,
.post-content h6 {
    color: var(--color-text-primary);
}

.post-content li {
    color: var(--color-text-primary);
}

.post-content blockquote {
    color: var(--color-text-secondary);
}

/* Override gradient background text color for blog posts */
.blog-background .post-content,
.blog-background .post-title,
.blog-background .post-content p,
.blog-background .post-content h1,
.blog-background .post-content h2,
.blog-background .post-content h3,
.blog-background .post-content h4,
.blog-background .post-content h5,
.blog-background .post-content h6,
.blog-background .post-content li {
    color: #eaeaea;
}

.blog-background .post-date,
.blog-background .post-content blockquote {
    color: #b0b0b0;
}
