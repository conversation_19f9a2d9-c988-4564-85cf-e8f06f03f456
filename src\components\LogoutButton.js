import React from 'react';
import { useOktaAuth } from '@okta/okta-react';

const LogoutButton = () => {
  const { oktaAuth } = useOktaAuth();

  const handleLogout = async () => {
    try {
      // Perform the logout
      await oktaAuth.signOut();
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  return (
    <button
      onClick={handleLogout}
      style={{
        background: 'transparent',
        color: 'inherit',
        border: 'none',
        padding: '0.5rem',
        cursor: 'pointer',
        fontSize: 'inherit',
        fontFamily: 'inherit'
      }}
    >
      Logout
    </button>
  );
};

export default LogoutButton;
