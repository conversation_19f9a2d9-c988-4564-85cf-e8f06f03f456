import React from 'react';
import { Router } from '@reach/router';
import SecureRoute from '../components/SecureRoute';
import Layout from '../components/layout';
import SEO from '../components/seo';
import LoginCallback from './login/callback';

// Example protected page
const Profile = () => (
  <Layout title="Profile">
    <SEO title="Profile" />
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '50vh',
      textAlign: 'center',
      padding: '2rem'
    }}>
      <h1>Profile Page</h1>
      <p>This is a protected page that only authenticated users can see.</p>
    </div>
  </Layout>
);

// App component with protected routes
const App = () => (
  <Router basepath="/app">
    <LoginCallback path="/login/callback" />
    <SecureRoute path="/profile" component={Profile} />
  </Router>
);

export default App;
