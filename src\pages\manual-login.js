import React, { useEffect, useState } from 'react';
import Layout from '../components/layout';
import SEO from '../components/seo';
import { getAuth0LoginUrl } from '../auth/auth0Service';

const ManualLogin = () => {
  const [loginUrl, setLoginUrl] = useState('');
  const isBrowser = typeof window !== 'undefined';

  useEffect(() => {
    if (isBrowser) {
      // Get the Auth0 login URL from our service
      const authUrl = getAuth0LoginUrl();
      setLoginUrl(authUrl);

      console.log('Generated Auth0 login URL:', authUrl);
    }
  }, [isBrowser]);

  return (
    <Layout title="Manual Login">
      <SEO title="Manual Login" />
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
        textAlign: 'center',
        padding: '2rem'
      }}>
        <h1>Manual Auth0 Login</h1>
        <p>Click the button below to manually log in with Auth0:</p>

        {loginUrl ? (
          <a
            href={loginUrl}
            style={{
              display: 'inline-block',
              background: '#0277bd',
              color: 'white',
              textDecoration: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '4px',
              marginTop: '1.5rem',
              fontSize: '1rem'
            }}
          >
            Login with Auth0
          </a>
        ) : (
          <p>Generating login URL...</p>
        )}

        <div style={{ marginTop: '2rem', textAlign: 'left', maxWidth: '600px' }}>
          <h3>Troubleshooting</h3>
          <p>If you're having trouble logging in, make sure:</p>
          <ul>
            <li>Your Auth0 application is configured as a "Single Page Application"</li>
            <li>The "Token Endpoint Authentication Method" is set to "None"</li>
            <li>You have added <code>{isBrowser ? window.location.origin : 'your-domain'}/login/callback</code> as an allowed callback URL in Auth0</li>
            <li>You have added <code>{isBrowser ? window.location.origin : 'your-domain'}</code> as an allowed web origin in Auth0</li>
          </ul>
        </div>
      </div>
    </Layout>
  );
};

export default ManualLogin;
