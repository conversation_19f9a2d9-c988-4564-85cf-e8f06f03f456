import React, { useEffect } from 'react';
import Layout from '../components/layout';
import SEO from '../components/seo';

// Configuration for Auth0
const auth0Config = {
  domain: 'dev-h4miq62iedog2a6m.us.auth0.com',
  clientId: 'BDEQBKCbIxFjpcDfqK5PWeI461jKCQ5m',
  redirectUri: typeof window !== 'undefined' ? `${window.location.origin}/login/callback` : '',
  scope: 'openid profile email',
  responseType: 'code',
};

// Client-side only component that handles the direct login redirect
const DirectLoginHandler = () => {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Clear any existing Auth0/Okta data from localStorage
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('okta-') || key.startsWith('auth0-')) {
          localStorage.removeItem(key);
        }
      });

      // Store return URL
      localStorage.setItem('okta-return-url', '/');

      // Generate a random state parameter to prevent CSRF
      const state = 'direct' + Math.random().toString(36).substring(2, 15);
      localStorage.setItem('auth0-state', state);

      // Create the Auth0 authorization URL
      const authUrl = `https://${auth0Config.domain}/authorize?` +
        `client_id=${auth0Config.clientId}&` +
        `redirect_uri=${encodeURIComponent(auth0Config.redirectUri)}&` +
        `response_type=${auth0Config.responseType}&` +
        `scope=${encodeURIComponent(auth0Config.scope)}&` +
        `state=${state}`;

      console.log('Redirecting to Auth0 login:', authUrl);
      
      // Redirect to Auth0 login
      window.location.href = authUrl;
    }
  }, []);

  return (
    <div style={{ textAlign: 'center', marginTop: '2rem' }}>
      <p>Redirecting to Auth0 login...</p>
      <div className="loading-spinner" style={{
        border: '4px solid #f3f3f3',
        borderTop: '4px solid #3498db',
        borderRadius: '50%',
        width: '30px',
        height: '30px',
        animation: 'spin 2s linear infinite',
        margin: '1rem auto'
      }}></div>
    </div>
  );
};

const DirectLogin = () => {
  const isBrowser = typeof window !== 'undefined';

  return (
    <Layout title="Direct Login">
      <SEO title="Direct Login" />
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
        textAlign: 'center',
        padding: '2rem'
      }}>
        <h1>Direct Auth0 Login</h1>
        <p>This page will redirect you directly to Auth0 for authentication.</p>
        <p>Please wait while we redirect you...</p>
        
        {/* Only render the DirectLoginHandler component on the client side */}
        {isBrowser && <DirectLoginHandler />}
        
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    </Layout>
  );
};

export default DirectLogin;
