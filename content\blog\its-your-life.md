---
path: 2020_3_its-your-life/
date: 2020-09-11T21:36:40.866Z
title: 🧭 It's Your Life
description: Make no mistake, it is your career, and more importantly, It's Your
  Life. You own it.
visibility: private
---
![](../assets/happy.jpg)

> **“I am not in this world to live up to your expectations  
> and you are not in this world to live up to mine.”**  
> — <PERSON>

**"The Pragmatic Programmer" by <PERSON> and <PERSON> is the best technical book I’ve ever read.**  
I received it as a Christmas gift from my supervisor, and it completely captivated me from the very first chapter. I ended up spending my entire holiday vacation reading it—page after page—unable to put it down. I was staying at a fancy spa hotel with a huge water park, a heated outdoor pool, and plenty of entertainment options... but my excitement was so obvious that strangers actually stopped to ask me what book I was reading.

Since then, I’ve found myself re-reading chapters whenever I face complex situations at work. Each time, the book offers new insight—relevant not just to software development, but to life. Honestly, this book feels like it was written for me. And if you’re reading this post, chances are it’s written for you too.

As software developers, we enjoy a rare combination of high demand, great compensation, and extraordinary flexibility—we can work from virtually anywhere. Yet, so many devs I meet feel stuck, undervalued, or uninspired. They want more, but don’t take action.

This quote from *The Pragmatic Programmer* hits the nail on the head:

> _“It is your life. You own it. You run it. You create it.”_

> *Many developers we talk to are frustrated. Their concerns are varied. Some feel they're stagnating in their job, others that technology has passed them by. Folks feel they are underappreciated or underpaid, or that their teams are toxic. Maybe they want to move to Asia, or Europe, or work from home.*
>
> *And the answer we give is always the same: “Why can't you change it?”*
>
> *Software development must appear close to the top of any list of careers where you have control. Our skills are in demand, our knowledge crosses geographic boundaries, we can work remotely. We're paid well. We really can do just about anything we want.*
>
> *But for some reason, developers seem to resist change. They hunker down and hope things will get better. They look on, passively, as their skills become dated and complain that their companies don't train them. They look at ads for exotic locations on the bus, then step off into the chilling rain and trudge into work.*
>
> **So here's the most important tip in the book:**  
> **You Have Agency.**
>
> *Does your work environment suck? Is your job boring? Try to fix it. But don’t try forever. As Martin Fowler says: “You can change your organization, or change your organization.”*
>
> *If technology seems to be passing you by, make time (on your own time) to study new stuff that looks interesting. You're investing in yourself, so doing it while you're off the clock is only reasonable.*
>
> *Want to work remotely? Have you asked? If they say no, then find someone who says yes.*
>
> *This industry gives you a remarkable set of opportunities. Be proactive, and take them.*

📖 *Thomas, D., & Hunt, A. (2020). The Pragmatic Programmer.*

---

**All I can add is this:**  
We, developers, have every resource we need to reach our full potential—and maybe even real happiness. The only thing standing in the way is whether we take responsibility for our own lives.


