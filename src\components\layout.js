import React from "react"
import styled from "styled-components"
import { Link } from "gatsby"
import { GlobalStyle } from "../styles/GlobalStyle"
import { rhythm } from "../utils/typography"
import { useLocation } from '@reach/router';

// Client-side only component for auth-related functionality
const AuthStatusBanner = () => {
  const [authenticated, setAuthenticated] = React.useState(false);
  const isBrowser = typeof window !== 'undefined';

  // Check authentication status on mount and when localStorage changes
  React.useEffect(() => {
    if (!isBrowser) return;

    // Import auth service dynamically to avoid SSR issues
    import('../auth/auth0Service').then(({ isAuthenticated }) => {
      setAuthenticated(isAuthenticated());
    });

    // Listen for storage events to update auth status
    const handleStorageChange = () => {
      import('../auth/auth0Service').then(({ isAuthenticated }) => {
        setAuthenticated(isAuthenticated());
      });
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [isBrowser]);

  const handleLogout = (e) => {
    e.preventDefault();
    if (isBrowser) {
      import('../auth/auth0Service').then(({ logout }) => {
        logout();
      });
    }
  };

  return (
    <AuthBanner>
      {authenticated ? (
        <span>
          Logged in | <Link to="/app/profile" style={{ color: 'inherit', marginRight: '10px' }}>Profile</Link> |
          <Link to="#" onClick={handleLogout} style={{ color: 'inherit' }}>Logout</Link>
        </span>
      ) : (
        <span>You are viewing public posts only | <Link to="/login" style={{ color: 'inherit' }}>Login</Link> to see private posts</span>
      )}
    </AuthBanner>
  );
};

const Layout = ({ title, children }) => {
  const location = useLocation();
  const isHomePage = location.pathname === "/"
  const isBrowser = typeof window !== 'undefined';

  return (
    <Wrapper>
      <GlobalStyle />
      {/* Login Status Banner - Only render on client side */}
      {isBrowser ? <AuthStatusBanner /> : <AuthBanner>Loading...</AuthBanner>}
      <Header>
        <TitleLink to="/blog">
          <Title className="site-title">{title}</Title>
        </TitleLink>
      </Header>
      <Main isHomePage={isHomePage}>{children}</Main>
      <Footer>
        © {new Date().getFullYear()}
      </Footer>
    </Wrapper>
  )
}

const Wrapper = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
`

const AuthBanner = styled.div`
  background-color: #f5f5f5;
  color: #666;
  padding: 8px;
  text-align: center;
  font-size: 0.8rem;
  border-bottom: 1px solid #ddd;
`

const Header = styled.header`
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
`

const TitleLink = styled(Link)`
  box-shadow: none;
  text-decoration: none;
  color: inherit;
`

const Title = styled.h1`
  margin-top: 0;
  color: var(--color-text-light); /* Using the light gray color (#999) */
`

const Main = styled.main`
  flex: 1;
  padding-inline: ${rhythm(1)};
  max-width: ${props => props.isHomePage ? rhythm(24) : rhythm(32)};
  margin: 0 auto;
  width: 100%;

  @media (max-width: 768px) {
    max-width: ${rhythm(24)};
  padding: ${rhythm(1)};
  }
`

const Footer = styled.footer`
  padding: ${rhythm(1)};
  text-align: center;
  font-size: 0.8rem;
`

export default Layout
