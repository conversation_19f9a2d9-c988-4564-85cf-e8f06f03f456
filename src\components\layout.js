import React from "react"
import styled from "styled-components"
import { Link } from "gatsby"
import { GlobalStyle } from "../styles/GlobalStyle"
import { rhythm } from "../utils/typography"
import { useLocation } from '@reach/router';
import { useOktaAuth } from '@okta/okta-react';

// Client-side only component for auth-related functionality
const AuthStatusBanner = () => {
  const { authState, oktaAuth } = useOktaAuth();
  const isAuthenticated = authState?.isAuthenticated;

  const handleLogout = async () => {
    try {
      if (oktaAuth) {
        await oktaAuth.signOut();
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <AuthBanner>
      {isAuthenticated === true ? (
        <span>Logged in | <Link to="#" onClick={handleLogout} style={{ color: 'inherit' }}>Logout</Link></span>
      ) : (
        <span>You are viewing public posts only | <Link to="/login" style={{ color: 'inherit' }}>Login</Link> to see private posts</span>
      )}
    </AuthBanner>
  );
};

const Layout = ({ title, children }) => {
  const location = useLocation();
  const isHomePage = location.pathname === "/"
  const isBrowser = typeof window !== 'undefined';

  return (
    <Wrapper>
      <GlobalStyle />
      {/* Login Status Banner - Only render on client side */}
      {isBrowser ? <AuthStatusBanner /> : <AuthBanner>Loading...</AuthBanner>}
      <Header>
        <TitleLink to="/blog">
          <Title className="site-title">{title}</Title>
        </TitleLink>
      </Header>
      <Main isHomePage={isHomePage}>{children}</Main>
      <Footer>
        © {new Date().getFullYear()}
      </Footer>
    </Wrapper>
  )
}

const Wrapper = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
`

const AuthBanner = styled.div`
  background-color: #f5f5f5;
  color: #666;
  padding: 8px;
  text-align: center;
  font-size: 0.8rem;
  border-bottom: 1px solid #ddd;
`

const Header = styled.header`
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
`

const TitleLink = styled(Link)`
  box-shadow: none;
  text-decoration: none;
  color: inherit;
`

const Title = styled.h1`
  margin-top: 0;
  color: var(--color-text-light); /* Using the light gray color (#999) */
`

const Main = styled.main`
  flex: 1;
  padding-inline: ${rhythm(1)};
  max-width: ${props => props.isHomePage ? rhythm(24) : rhythm(32)};
  margin: 0 auto;
  width: 100%;

  @media (max-width: 768px) {
    max-width: ${rhythm(24)};
  padding: ${rhythm(1)};
  }
`

const Footer = styled.footer`
  padding: ${rhythm(1)};
  text-align: center;
  font-size: 0.8rem;
`

export default Layout
