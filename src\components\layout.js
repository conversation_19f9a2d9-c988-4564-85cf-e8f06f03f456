import React from "react"
import styled from "styled-components"
import { Link } from "gatsby"
import { GlobalStyle } from "../styles/GlobalStyle"
import { rhythm } from "../utils/typography"
import { useLocation } from '@reach/router';
import { useOktaAuth } from '@okta/okta-react';

// Client-side only component for auth-related functionality
const AuthStatusBanner = () => {
  const { authState, oktaAuth } = useOktaAuth();
  const isAuthenticated = authState?.isAuthenticated;

  const handleLogout = async () => {
    try {
      if (oktaAuth) {
        await oktaAuth.signOut();
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <AuthBanner>
      {isAuthenticated === true ? (
        <span>Logged in | <Link to="#" onClick={handleLogout} style={{ color: 'inherit' }}>Logout</Link></span>
      ) : (
        <span>You are viewing public posts only | <Link to="/login" style={{ color: 'inherit' }}>Login</Link> to see private posts</span>
      )}
    </AuthBanner>
  );
};

const Layout = ({ title, children }) => {
  const location = useLocation();
  const isHomePage = location.pathname === "/"
  const isBrowser = typeof window !== 'undefined';

  return (
    <Wrapper>
      <GlobalStyle />
      {/* Login Status Banner - Only render on client side */}
      {isBrowser ? <AuthStatusBanner /> : <AuthBanner>Loading...</AuthBanner>}
      <Header>
        <TitleLink to="/blog">
          <Title className="site-title">{title}</Title>
        </TitleLink>
      </Header>
      <Main isHomePage={isHomePage}>{children}</Main>
      <Footer>
        © {new Date().getFullYear()}
      </Footer>
    </Wrapper>
  )
}

const Wrapper = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
`

const AuthBanner = styled.div`
  background-color: #333;
  color: white;
  text-align: center;
  padding: 0.5rem;
  font-size: 0.8rem;
`

const Header = styled.header`
  padding: ${rhythm(1)};
  background-color: #f8f8f8;
  border-bottom: 1px solid #eee;
`

const TitleLink = styled(Link)`
  box-shadow: none;
  text-decoration: none;
  color: inherit;
`

const Title = styled.h1`
  margin: 0;
  font-size: 1.5rem;
`

const Main = styled.main`
  flex: 1;
  padding: ${rhythm(1)};
  padding-top: ${props => props.isHomePage ? 0 : rhythm(1)};
`

const Footer = styled.footer`
  padding: ${rhythm(1)};
  background-color: #f8f8f8;
  border-top: 1px solid #eee;
  text-align: center;
  font-size: 0.8rem;
`

export default Layout
