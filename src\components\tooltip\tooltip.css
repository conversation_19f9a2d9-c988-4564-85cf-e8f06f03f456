.tooltip-container {
  /* position: relative; */
  display: inline-block;
}

.tooltip-text {
  visibility: hidden;
  width: auto;
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
  text-align: center;
  border-radius: 4px;
  padding: 5px 10px;
  position: absolute;
  z-index: 100;
  bottom: 70%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  font-size: var(--font-size-small);
  white-space: nowrap;
}

.tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--color-background-secondary) transparent transparent transparent;
}

.tooltip-container:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}