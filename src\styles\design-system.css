:root {
  /* Colors */
  /* Base Colors */
  --color-primary: #333;
  --color-secondary: #007acc;
  --color-blue: #7FB7E4;
  --color-white: #fff;
  --color-orange: #ff9800;
  --color-green: #92b901;
  --color-lightgrey: #e0e0e0;
  --color-bluegrey: #f5f7fa;
  --color-grey:#d0d7de;



  /* Text Colors */
  --color-text-primary: #3b3b3b;
  --color-text-secondary: #606770;
  --color-text-muted: #484646;
  --color-text-light: #999;
  --color-title: #333;
  --color-subtitle: #888;
  --color-unfocused: #bbb;
  --color-test-description: #666;
  --color-axillary: #dfe1e5;
  --color-placeholder: #9aa0a6;

  /* Dark Theme Text Colors */
  --color-text-primary-dark: #eaeaea;
  --color-text-secondary-dark: #b0b0b0;

  /* Background Colors */
  --color-background-primary: #fff;
  --color-background-secondary: #fdfdfd;
  --color-banner: #dfe1e5;

  /* Border Colors */
  --color-border-primary: var(--color-secondary);

  /* Shadow Colors */
  --color-shadow-primary: rgba(0, 0, 0, 0.1);

  /* Typography */
  --font-primary: Montserrat, sans-serif;
  --font-secondary: "Source Code Pro", monospace;
  --font-heading: 'Merriweather', 'Georgia', serif;
  --font-special: "Open Sans";
  --font-body: "Open Sans";

  /* Font Sizes */
  --font-size-tiny: 1rem;
  --font-size-small: 1.2rem;
  --font-size-mobile: 1.2rem;
  --font-size-primary: 1.3rem;
  --font-size-body: 1.5rem;
  --font-size-large: 1.8rem;
  --font-size-header: 2.25rem;
  --font-size-h1: 3rem;
  --font-size-h2: 2.25rem;
  --font-size-h3: 1.5rem;
  --font-size-h4: 1.5rem;
  --font-size-h5: 1.25rem;
  --font-size-h6: 1rem;

  /* Font Weights */
  --font-weight-normal: 400;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Letter Spacing */
  --letter-spacing-normal: 0.1em;
  --letter-spacing-wide: 0.2em;

  /* Spacing */
  --spacing-small: 0.5rem;
  --spacing-medium: 1rem;
  --spacing-large: 2rem;

  /* Shadows */
  --shadow-small: 4px 8px 4px 0 rgba(0, 0, 0, 0.1);
  --shadow-medium: 4px 8px 16px 0 rgba(0, 0, 0, 0.1);
  --shadow-large: 10px 10px 15px 0 rgba(0, 0, 0, 0.3);

  /* Animation Durations */
  --duration-fast: 0.2s;
  --duration-default: 0.3s;
  --duration-slow: 0.6s;
  --duration-very-slow: 1s;

  /* Breakpoints */
  --breakpoint-mobile: 768px;
  --breakpoint-tablet: 1024px;

  /* Z-index */
  --z-modal: 1000;
  --z-overlay: 900;
  --z-dropdown: 800;
  --z-header: 700;
  --z-footer: 600;

  /* Add if not already present */
  --border-radius-small: 5px;
  --animation-timing-default: ease;
  --border-width-default: 4px;
}

/* Base Typography Styles */
body {
  font-family: var(--font-primary);
  font-size: var(--font-size-body);
  color: var(--color-text-primary);
  line-height: 1.5;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-heading);
  letter-spacing: var(--letter-spacing-normal);
}

h1 {
  font-size: var(--font-size-heading);
  margin: var(--spacing-small) 0;
}

/* Utility Classes */
.text-primary {
  color: var(--color-text-primary);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-muted {
  color: var(--color-text-muted);
}

.bg-primary {
  background-color: var(--color-background-primary);
}

.bg-secondary {
  background-color: var(--color-background-secondary);
}

.shadow-small {
  box-shadow: var(--shadow-small);
}

.shadow-medium {
  box-shadow: var(--shadow-medium);
}

.shadow-large {
  box-shadow: var(--shadow-large);
}

/* Animation Classes */
.animate-fast {
  transition: all var(--duration-fast) ease;
}

.animate-default {
  transition: all var(--duration-default) ease;
}

.animate-slow {
  transition: all var(--duration-slow) ease-in-out;
}

/* Responsive Design */
@media (max-width: var(--breakpoint-mobile)) {
  body {
    font-size: var(--font-size-small);
  }

  h1 {
    font-size: calc(var(--font-size-heading) * 0.8);
  }
}