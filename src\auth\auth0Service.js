// Simple Auth0 service without using Okta SDK
const auth0Config = {
  domain: 'dev-h4miq62iedog2a6m.us.auth0.com',
  clientId: 'BDEQBKCbIxFjpcDfqK5PWeI461jKCQ5m',
  redirectUri: typeof window !== 'undefined' ? window.location.origin + '/login/callback' : '',
  scope: 'openid profile email',
  responseType: 'code',
};

// Get the Auth0 login URL
export const getAuth0LoginUrl = () => {
  if (typeof window === 'undefined') return '';

  // Generate a random state parameter to prevent CSRF
  const state = 'auth0_' + Math.random().toString(36).substring(2, 15);
  localStorage.setItem('auth0_state', state);

  // Create the Auth0 authorization URL
  return `https://${auth0Config.domain}/authorize?` +
    `client_id=${auth0Config.clientId}&` +
    `redirect_uri=${encodeURIComponent(auth0Config.redirectUri)}&` +
    `response_type=${auth0Config.responseType}&` +
    `scope=${encodeURIComponent(auth0Config.scope)}&` +
    `state=${state}`;
};

// Handle the Auth0 callback
export const handleAuth0Callback = async () => {
  if (typeof window === 'undefined') return { success: false, error: 'Not in browser' };

  try {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    const error = urlParams.get('error');
    const errorDescription = urlParams.get('error_description');

    // Check for Auth0 errors
    if (error) {
      console.error('Auth0 returned an error:', error, errorDescription);
      return {
        success: false,
        error: `Auth0 error: ${error}${errorDescription ? ': ' + errorDescription : ''}`
      };
    }

    // Check if we have an authorization code
    if (!code) {
      console.error('No authorization code found in URL');
      return { success: false, error: 'No authorization code found' };
    }

    // Verify state to prevent CSRF
    const storedState = localStorage.getItem('auth0_state');
    if (!state || state !== storedState) {
      console.error('Invalid state parameter');
      return { success: false, error: 'Invalid state parameter' };
    }

    // Exchange the code for tokens
    const tokenResponse = await fetch(`https://${auth0Config.domain}/oauth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        grant_type: 'authorization_code',
        client_id: auth0Config.clientId,
        code,
        redirect_uri: auth0Config.redirectUri
      })
    });

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.json();
      console.error('Error exchanging code for tokens:', errorData);
      return { success: false, error: errorData.error_description || 'Failed to exchange code for tokens' };
    }

    const tokens = await tokenResponse.json();

    // Store tokens in localStorage
    localStorage.setItem('auth0_access_token', tokens.access_token);
    localStorage.setItem('auth0_id_token', tokens.id_token);
    localStorage.setItem('auth0_expires_at', JSON.stringify(Date.now() + tokens.expires_in * 1000));

    // Clean up state
    localStorage.removeItem('auth0_state');

    return { success: true };
  } catch (error) {
    console.error('Error handling Auth0 callback:', error);
    return { success: false, error: error.message || 'Unknown error during login process' };
  }
};

// Check if the user is authenticated
export const isAuthenticated = () => {
  if (typeof window === 'undefined') return false;

  const expiresAt = JSON.parse(localStorage.getItem('auth0_expires_at') || '0');
  return Date.now() < expiresAt && !!localStorage.getItem('auth0_id_token');
};

// Logout the user
export const logout = () => {
  if (typeof window === 'undefined') return;

  // Clear Auth0 tokens from localStorage
  localStorage.removeItem('auth0_access_token');
  localStorage.removeItem('auth0_id_token');
  localStorage.removeItem('auth0_expires_at');
  localStorage.removeItem('auth0_state');

  console.log('Logging out, tokens cleared from localStorage');

  // For Auth0, we can simply redirect to home page after clearing tokens
  // This avoids issues with Auth0's logout endpoint configuration
  window.location.href = window.location.origin;

  /*
  // NOTE: If you want to use Auth0's logout endpoint, you need to configure
  // the "Allowed Logout URLs" in your Auth0 application settings.
  // The code below is commented out because it requires proper Auth0 configuration:

  window.location.href = `https://${auth0Config.domain}/v2/logout?` +
    `client_id=${auth0Config.clientId}&` +
    `returnTo=${encodeURIComponent(window.location.origin)}`;
  */
};
