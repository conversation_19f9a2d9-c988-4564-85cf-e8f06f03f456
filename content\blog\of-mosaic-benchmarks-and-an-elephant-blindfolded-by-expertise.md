---
path: 2024_12_missed_elephant
date: 2024-12-30T19:57:19.771Z
title: 🧩Of Mosaic Benchmarks and an Elephant Blindfolded by Expertise
description: " A visit to a reputable math studio turned into an unexpected
  lesson in how expertise can sometimes miss the bigger picture. What happens
  when brilliance is measured by the wrong benchmarks? "
---
![](../assets/gettyimages-522959656-612x612.jpg)

Once upon a time, I decided to take my 5-year-old daughter, <PERSON>, to a highly reputable math studio for an introductory visit. The owner of the studio, armed with a solid math education and 20 years of experience running programs for K-12 students, graciously scheduled time for us. I was excited—what could possibly go wrong?

When we arrived, she pulled out a mosaic board, carefully built a simple pattern on one side, and then turned her attention to chatting with me. Meanwhile, <PERSON>, curious as ever, decided to explore the mosaic herself. She built her own design on the other side of the board—a design that, unbeknownst to her, was being evaluated.

Here’s the kicker: the studio owner never gave <PERSON> any instructions. No one told her to copy the example on the other side of the board. <PERSON> simply did what 5-year-olds do—she created something entirely her own, much more intricate and sophisticated than the original.

After about 10 minutes, the studio owner delivered her grand conclusion: *“Your daughter is not ready for math classes yet.”* She explained—quite verbosely, I might add—how children need to master units, then tens, then hundreds before they can even *dream* of understanding math. To justify her verdict, she pointed to <PERSON>’s mosaic design and concluded that, because <PERSON> didn’t match the sample pattern, she must not grasp the concept of units and tens. My eyebrows raised higher with each sentence, like parabolas shooting off the graph. “Huh?” I was trying to make sense of her algorithm for evaluating math readiness.

“Come back in a year,” she said. I smiled and thanked her for her time. I knew one thing for sure—we weren’t coming back.

Why? Because I had just witnessed this experienced lady miss something glaringly obvious — like an elephant in the room holding a calculator. It was a spectacular demonstration of what happens when one relies on rigid stereotypes and fails to recognize the potential of anything that deviates slightly from the "average." Instead of expanding and evolving to make space for unconventional capabilities, the system squashed them with misplaced assumptions.

In case you’re wondering how my own evaluation system makes sense, let me provide a bit more context. Just a few weeks earlier, Alex approached me holding her stuffed bunny and asked, *“The bunny is curious—can 2 + 2 = 5 ever be correct?”*

I know this may not sound like a typical 5-year-old question, but this was classic Alex. Her curiosity was boundless and her thinking refreshingly unconventional.  I replied, *“In some contexts, it can be. There are actually several ways that could make sense mathematically. Can you find them?”*

And guess what? She did! Alex came up with three solutions:

1. Numbers are symbols we've agreed upon. If we create our own [math ring](https://en.wikipedia.org/wiki/Ring_(mathematics)) and redefine what “2,” “+,” and “5” mean, we could make 2 + 2 = 5 true.
2. Operations like “+” and “=” are conventions. Reassigning their functions opens up new possibilities.
3. Finally, consider dynamics: if the two '2’s' grew by 0.5 each during an [infinitesimally](https://en.wikipedia.org/wiki/Infinitesimal) small moment while performing the addition, the result would indeed be 5.

I was so impressed that I shared Alex’s ideas with [Bob and Ellen Kaplan](https://people.math.harvard.edu/~knill/various/bobkaplan/index.html), math professors at Harvard and founders of the Math Circle Alex attended. They validated her thinking as mathematically sound and praised her creativity.

For context, Alex and I had already explored [binary](https://en.wikipedia.org/wiki/Binary_number), [decimal](https://en.wikipedia.org/wiki/Decimal), and [*n*-number systems](https://en.wikipedia.org/wiki/Positional_notation). She quickly grasped that the decimal system is just one of many ways to think about numbers. How was this possible? With engaging, age-appropriate resources, advanced math becomes both accessible and fun. These tools, such as the book [*Alice in Math Wonderland* by Lev Gendenshtein](https://www.rulit.me/books/alisa-v-strane-matematiki-read-164757-1.html), the *[AoPS Beast Academy](https://beastacademy.com/)* program, games from *Banda Umnikov*, weekly Math Circle with Bob & Ellen, combine stories, games, and creative exploration to make learning both engaging and enjoyable.

And speaking of "traditional" conventional public school math... Two weeks later, [Math Kangaroo](https://mathkangaroo.org/mks/) results came in: Alex ranked [1st nationwide](https://mathkangaroo.org/mks/wp-content/uploads/2022/04/2017_Level-1_National-Winners.pdf) in her age category. A week after that, her IQ test results arrived—she scored above 130. Both the math olympiad and the cognitive test were conducted in English, which, at the time, was her second language with limited exposure.

The moral of the story? Rigid benchmarks often fall short when they encounter exceptions. True talent lies not in simply following formulas, but in thinking beyond them. After all, evolution wouldn’t happen without the courage to try things differently.

Whether in education, work, or life, let’s celebrate the courage to innovate, explore, and deviate from the norm—because that’s how true progress is made. And remember—if you’re going to evaluate someone’s readiness, make sure to check for elephants—and maybe stuffed bunnies—before jumping to conclusions.

![](../assets/alex-collage.jpg)