---
path: 2020_5_stone-soup/
date: 2020-09-19T03:01:55.770Z
title: 🌱 Stone Soup vs Boiled Frog
description: When you are try to catalyze change within your organization  are
  you doing good or bad? Is the decision subjective or objective?
visibility: private
---
![](../assets/stoned-soup-2.jpg "soup")

**Another gem from one of my favorite books — and a timeless lesson in leadership, influence, and change.**

If you’ve ever tried to drive change in a resistant organization, deliver innovation without a budget, or navigate the tension between intention and perception… this one’s for you. 🍲🐸

- - -

**The Stone Soup Lesson (from *The Pragmatic Programmer*)**

> The three soldiers returning home from war were hungry. When they saw the village ahead their spirits lifted—they were sure the villagers would give them a meal. But when they got there, they found the doors locked and the windows closed. After many years of war, the villagers were short of food, and hoarded what they had.
>
> Undeterred, the soldiers boiled a pot of water and carefully placed three stones into it. The amazed villagers came out to watch.
>
> "This is stone soup," the soldiers explained.
> "Is that all you put in it?" asked the villagers.
> "Absolutely—although some say it tastes even better with a few carrots."
> A villager ran off, returning with a basket of carrots from his hoard.
>
> A couple of minutes later, the villagers again asked, "Is that it?"
> "Well," said the soldiers, "a couple of potatoes give it body."
> Off ran another villager.
>
> Over the next hour, the soldiers listed more ingredients that would enhance the soup: beef, leeks, salt, and herbs. Each time, a different villager ran off to raid their personal stores.
>
> Eventually they had produced a large pot of steaming soup. The soldiers removed the stones, and they sat down with the entire village to enjoy the first square meal any of them had eaten in months.
>
> There are a couple of morals in the stone soup story. The villagers are tricked by the soldiers, who use the villagers' curiosity to get food from them. But more importantly, the soldiers act as a catalyst, bringing the village together so they can jointly produce something they couldn’t have done by themselves—a synergistic result. Eventually, everyone wins.
>
> Every now and then, you might want to emulate the soldiers.
>
> You may be in a situation where you know exactly what needs doing and how to do it. The entire system just appears before your eyes—you know it's right. But ask permission to tackle the whole thing and you'll be met with delays and blank stares. People will form committees, budgets will need approval, and things will get complicated. Everyone will guard their own resources. Sometimes this is called "start-up fatigue."
>
> It's time to bring out the stones. Work out what you can reasonably ask for. Develop it well. Once you've got it, show people, and let them marvel. Then say, "of course, it would be better if we added..." Pretend it's not important. Sit back and wait for them to start asking you to add the functionality you originally wanted. People find it easier to join an ongoing success. Show them a glimpse of the future and you'll get them to rally around.
>
> **The Villagers’ Side**
> On the other hand, the stone soup story is also about gentle and gradual deception. It’s about focusing too tightly. The villagers think about the stones and forget about the rest of the world. We all fall for it, every day. Things just creep up on us.
>
> We’ve all seen the symptoms. Projects slowly and inexorably get totally out of hand. Most software disasters start out too small to notice, and most project overruns happen a day at a time. Systems drift from their specifications feature by feature, while patch after patch gets added to a piece of code until there’s nothing of the original left. It’s often the accumulation of small things that breaks morale and teams.
>
> We’ve never tried this, honest. But they say that if you take a frog and drop it into boiling water, it will jump straight back out again. However, if you place the frog in a pan of cold water, then gradually heat it, the frog won’t notice the slow increase in temperature and will stay put until cooked.
>
> Note that the frog’s problem is different from the broken windows issue discussed in Section 2. In the Broken Window Theory, people lose the will to fight entropy because they perceive that no one else cares. The frog just doesn’t notice the change.
>
> Don’t be like the frog. Keep an eye on the big picture. Constantly review what’s happening around you, not just what you personally are doing.
>
> While reviewing a draft of this book, John Lakos raised the following issue: The soldiers progressively deceive the villagers, but the change they catalyze does them all good. However, by progressively deceiving the frog, you’re doing it harm. Can you determine whether you’re making stone soup or frog soup when you try to catalyze change? Is the decision subjective or objective?

📖 *Excerpt from* **[“The Pragmatic Programmer” by David Thomas and Andrew Hunt](https://pragprog.com/titles/tpp20/the-pragmatic-programmer-20th-anniversary-edition/)**

- - -

**🌱 My takeaway?**

Sometimes, leadership means planting the first stone in the pot—and letting others discover the vision along the way. But the real challenge is making sure you’re cooking **stone soup**, not **frog soup**.
So lead, catalyze, inspire—but always keep an eye on the temperature *and* the ingredients you put in.
